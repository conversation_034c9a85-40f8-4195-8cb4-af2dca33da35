# Jewelry Tracker - WAMP Server Deployment Guide

## Prerequisites Checklist
- ✅ WAMP Server installed and running
- ✅ MySQL database `jewelry_tracker` created
- ✅ Database tables created using `database-setup.sql`
- ✅ Node.js installed on the system

## Current Configuration
- **Database Name**: `jewelry_tracker`
- **Username**: `root`
- **Password**: `jewelry123`
- **Host**: `localhost`
- **Port**: `3306`

## Step-by-Step Deployment

### Step 1: Verify WAMP Server Status
1. Open WAMP Server from system tray
2. Ensure both **Apache** and **MySQL** services are **GREEN** (running)
3. If not green, click on the service and select "Start Service"

### Step 2: Verify Database Setup
1. Open browser and go to: `http://localhost/phpmyadmin`
2. Login with username: `root`, password: `jewelry123`
3. Check if database `jewelry_tracker` exists
4. Verify all tables are created (customers, items, workers, metals, purities, etc.)

### Step 3: Fix the Startup Script Issue

The batch file is closing because of an error. Let's create a better startup script:

**Create `start-jewelry-tracker.bat`:**
```batch
@echo off
title Jewelry Tracker Server
echo ========================================
echo    Jewelry Tracker Application
echo ========================================
echo.

echo Checking Node.js installation...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js is installed successfully.
echo.

echo Installing/Updating dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    pause
    exit /b 1
)

echo.
echo Starting Jewelry Tracker Server...
echo.
echo ========================================
echo  Server will start on: http://localhost:3000
echo  Login: <EMAIL>
echo  Password: Admin@1234
echo ========================================
echo.
echo Press Ctrl+C to stop the server
echo.

call npm run dev
pause
```

### Step 4: Alternative Startup Methods

**Method 1: Using Command Prompt**
1. Open Command Prompt as Administrator
2. Navigate to project folder:
   ```cmd
   cd C:\path\to\your\jewelry-tracker-project
   ```
3. Install dependencies:
   ```cmd
   npm install
   ```
4. Start the server:
   ```cmd
   npm run dev
   ```

**Method 2: Using PowerShell**
1. Right-click on project folder
2. Select "Open PowerShell window here"
3. Run commands:
   ```powershell
   npm install
   npm run dev
   ```

### Step 5: Verify Server Startup
When the server starts successfully, you should see:
```
✅ Database connected successfully
✅ Database initialized successfully
🚀 Server running on http://localhost:3000
```

### Step 6: Access the Application
1. Open your web browser
2. Go to: `http://localhost:3000`
3. You should see the login page
4. Login with:
   - **Username**: `<EMAIL>`
   - **Password**: `Admin@1234`

## Troubleshooting Common Issues

### Issue 1: "Database connection failed"
**Solution:**
1. Check if WAMP MySQL service is running (green in WAMP tray)
2. Verify database credentials in `server/database.ts`
3. Test database connection in phpMyAdmin

### Issue 2: "Port 3000 already in use"
**Solution:**
1. Close any other applications using port 3000
2. Or change port in `server/index.ts` (line 61):
   ```typescript
   const PORT = 3001; // Change to different port
   ```

### Issue 3: "npm command not found"
**Solution:**
1. Reinstall Node.js from https://nodejs.org/
2. Restart Command Prompt/PowerShell
3. Verify installation: `node --version` and `npm --version`

### Issue 4: Batch file closes immediately
**Solution:**
1. Run Command Prompt as Administrator
2. Navigate to project folder manually
3. Run `npm install` then `npm run dev`

### Issue 5: "Module not found" errors
**Solution:**
1. Delete `node_modules` folder
2. Delete `package-lock.json` file
3. Run `npm install` again

## Manual Database Password Update

If you need to change the database password:

1. **Update in code** (`server/database.ts`):
   ```typescript
   export const dbConfig = {
     host: 'localhost',
     user: 'root',
     password: 'YOUR_NEW_PASSWORD', // Change this
     database: 'jewelry_tracker',
     port: 3306,
     // ... rest of config
   };
   ```

2. **Update in WAMP**:
   - Open phpMyAdmin
   - Go to User accounts
   - Edit root user
   - Change password

## Production Deployment Tips

### For Client Use:
1. **Create Desktop Shortcut**:
   - Right-click on `start-jewelry-tracker.bat`
   - Create shortcut
   - Move shortcut to Desktop

2. **Auto-start with Windows** (Optional):
   - Press `Win + R`, type `shell:startup`
   - Copy the batch file to this folder

3. **Firewall Settings**:
   - Allow Node.js through Windows Firewall
   - Allow port 3000 for local network access

### For Network Access:
If you want other computers to access the application:

1. **Find your IP address**:
   ```cmd
   ipconfig
   ```

2. **Update server configuration** in `server/index.ts`:
   ```typescript
   server.listen(PORT, '0.0.0.0', () => {
     log(`🚀 Server running on http://localhost:${PORT}`);
   });
   ```

3. **Access from other computers**:
   `http://YOUR_IP_ADDRESS:3000`

## Success Verification

Your deployment is successful when:
- ✅ WAMP services are running (green)
- ✅ Database connection successful
- ✅ Server starts without errors
- ✅ Login page loads at `http://localhost:3000`
- ✅ Can login with admin credentials
- ✅ Dashboard shows with sample data
- ✅ Can add/edit customers, items, etc.
- ✅ Data persists after server restart

## Support Commands

**Check if server is running:**
```cmd
netstat -an | findstr :3000
```

**Kill process on port 3000:**
```cmd
netstat -ano | findstr :3000
taskkill /PID [PID_NUMBER] /F
```

**View server logs:**
Check the Command Prompt/PowerShell window where server is running

---

## Quick Start Summary

1. **Start WAMP** → Ensure MySQL is green
2. **Open Command Prompt** as Administrator
3. **Navigate** to project folder
4. **Run**: `npm install`
5. **Run**: `npm run dev`
6. **Open browser**: `http://localhost:3000`
7. **Login**: `<EMAIL>` / `Admin@1234`

**🎉 Your Jewelry Tracker is now running on WAMP server!**
