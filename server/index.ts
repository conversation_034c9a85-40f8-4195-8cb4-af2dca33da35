import express, { type Request, Response, NextFunction } from "express";
import cookieParser from "cookie-parser";
import { setupVite, serveStatic, log } from "./vite";
import { initializeDatabase, testConnection } from "./database";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Test database connection and choose appropriate routes
  const dbConnected = await testConnection();

  let registerRoutes;
  if (dbConnected) {
    log("✅ Using MySQL database");
    const { registerRoutes: mysqlRoutes } = await import("./routes-mysql");
    registerRoutes = mysqlRoutes;
    await initializeDatabase();
  } else {
    log("⚠️  MySQL not available, using local storage");
    const { registerRoutes: localRoutes } = await import("./routes");
    registerRoutes = localRoutes;
  }

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Serve the app on port 3000 or environment PORT
  // this serves both the API and the client.
  const port = process.env.PORT || 3000;
  server.listen(port, "localhost", () => {
    log(`🚀 Server running on http://localhost:${port}`);
  });
})();
