import {
  users, type User, type InsertUser,
  customers, type Customer, type InsertCustomer,
  items, type Item, type InsertItem,
  workers, type Worker, type InsertWorker,
  metals, type Metal, type InsertMetal,
  purity, type Purity, type InsertPurity,
  itemInward, type ItemInward, type InsertItemInward,
  itemOutward, type ItemOutward, type InsertItemOutward,
  metalInward, type MetalInward, type InsertMetalInward,
  metalOutward, type MetalOutward, type InsertMetalOutward,
  sales, type Sales, type InsertSales
} from "@shared/schema";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Customer methods
  getCustomers(): Promise<Customer[]>;
  getCustomer(id: string): Promise<Customer | undefined>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: string, customer: Partial<InsertCustomer>): Promise<Customer | undefined>;
  deleteCustomer(id: string): Promise<boolean>;

  // Item methods
  getItems(): Promise<Item[]>;
  getItem(id: string): Promise<Item | undefined>;
  createItem(item: InsertItem): Promise<Item>;
  updateItem(id: string, item: Partial<InsertItem>): Promise<Item | undefined>;
  deleteItem(id: string): Promise<boolean>;

  // Worker methods
  getWorkers(): Promise<Worker[]>;
  getWorker(id: string): Promise<Worker | undefined>;
  createWorker(worker: InsertWorker): Promise<Worker>;
  updateWorker(id: string, worker: Partial<InsertWorker>): Promise<Worker | undefined>;
  deleteWorker(id: string): Promise<boolean>;

  // Metal methods
  getMetals(): Promise<Metal[]>;
  getMetal(id: string): Promise<Metal | undefined>;
  createMetal(metal: InsertMetal): Promise<Metal>;
  updateMetal(id: string, metal: Partial<InsertMetal>): Promise<Metal | undefined>;
  deleteMetal(id: string): Promise<boolean>;

  // Purity methods
  getPurities(): Promise<Purity[]>;
  getPurity(id: string): Promise<Purity | undefined>;
  createPurity(purity: InsertPurity): Promise<Purity>;
  updatePurity(id: string, purity: Partial<InsertPurity>): Promise<Purity | undefined>;
  deletePurity(id: string): Promise<boolean>;

  // Item Inward methods
  getItemInwards(): Promise<ItemInward[]>;
  getItemInward(id: string): Promise<ItemInward | undefined>;
  createItemInward(itemInward: InsertItemInward): Promise<ItemInward>;
  updateItemInward(id: string, itemInward: Partial<InsertItemInward>): Promise<ItemInward | undefined>;
  deleteItemInward(id: string): Promise<boolean>;

  // Item Outward methods
  getItemOutwards(): Promise<ItemOutward[]>;
  getItemOutward(id: string): Promise<ItemOutward | undefined>;
  createItemOutward(itemOutward: InsertItemOutward): Promise<ItemOutward>;
  updateItemOutward(id: string, itemOutward: Partial<InsertItemOutward>): Promise<ItemOutward | undefined>;
  deleteItemOutward(id: string): Promise<boolean>;

  // Metal Inward methods
  getMetalInwards(): Promise<MetalInward[]>;
  getMetalInward(id: string): Promise<MetalInward | undefined>;
  createMetalInward(metalInward: InsertMetalInward): Promise<MetalInward>;
  updateMetalInward(id: string, metalInward: Partial<InsertMetalInward>): Promise<MetalInward | undefined>;
  deleteMetalInward(id: string): Promise<boolean>;

  // Metal Outward methods
  getMetalOutwards(): Promise<MetalOutward[]>;
  getMetalOutward(id: string): Promise<MetalOutward | undefined>;
  createMetalOutward(metalOutward: InsertMetalOutward): Promise<MetalOutward>;
  updateMetalOutward(id: string, metalOutward: Partial<InsertMetalOutward>): Promise<MetalOutward | undefined>;
  deleteMetalOutward(id: string): Promise<boolean>;

  // Sales methods
  getSales(): Promise<Sales[]>;
  getSale(id: string): Promise<Sales | undefined>;
  createSale(sale: InsertSales): Promise<Sales>;
  updateSale(id: string, sale: Partial<InsertSales>): Promise<Sales | undefined>;
  deleteSale(id: string): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private customers: Map<string, Customer>;
  private items: Map<string, Item>;
  private workers: Map<string, Worker>;
  private metals: Map<string, Metal>;
  private purities: Map<string, Purity>;
  private itemInwards: Map<string, ItemInward>;
  private itemOutwards: Map<string, ItemOutward>;
  private metalInwards: Map<string, MetalInward>;
  private metalOutwards: Map<string, MetalOutward>;
  private salesData: Map<string, Sales>;

  private currentUserId: number;
  private counters: Record<string, number>;

  constructor() {
    this.users = new Map();
    this.customers = new Map();
    this.items = new Map();
    this.workers = new Map();
    this.metals = new Map();
    this.purities = new Map();
    this.itemInwards = new Map();
    this.itemOutwards = new Map();
    this.metalInwards = new Map();
    this.metalOutwards = new Map();
    this.salesData = new Map();

    this.currentUserId = 1;
    this.counters = {
      customer: 1,
      item: 1,
      worker: 1,
      metal: 1,
      purity: 1,
      itemInward: 1,
      itemOutward: 1,
      metalInward: 1,
      metalOutward: 1,
      sales: 1,
    };
  }

  private generateId(prefix: string): string {
    const count = this.counters[prefix]++;
    return `${prefix.toUpperCase()}${count.toString().padStart(3, '0')}`;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.username === username);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Customer methods
  async getCustomers(): Promise<Customer[]> {
    return Array.from(this.customers.values());
  }

  async getCustomer(id: string): Promise<Customer | undefined> {
    return this.customers.get(id);
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    const id = insertCustomer.id || this.generateId('customer');
    const customer: Customer = {
      ...insertCustomer,
      id,
      createdAt: new Date(),
      createdBy: insertCustomer.createdBy || "<EMAIL>",
      status: insertCustomer.status || "Active",
      updatedAt: null,
      updatedBy: null
    };
    this.customers.set(id, customer);
    return customer;
  }

  async updateCustomer(id: string, customerData: Partial<InsertCustomer>): Promise<Customer | undefined> {
    const existing = this.customers.get(id);
    if (!existing) return undefined;

    const updated = {
      ...existing,
      ...customerData,
      updatedAt: new Date(),
      updatedBy: "<EMAIL>"
    };
    this.customers.set(id, updated);
    return updated;
  }

  async deleteCustomer(id: string): Promise<boolean> {
    return this.customers.delete(id);
  }

  // Item methods
  async getItems(): Promise<Item[]> {
    return Array.from(this.items.values());
  }

  async getItem(id: string): Promise<Item | undefined> {
    return this.items.get(id);
  }

  async createItem(insertItem: InsertItem): Promise<Item> {
    const id = insertItem.id || this.generateId('item');
    const item: Item = { 
      ...insertItem, 
      id,
      createdAt: new Date()
    };
    this.items.set(id, item);
    return item;
  }

  async updateItem(id: string, itemData: Partial<InsertItem>): Promise<Item | undefined> {
    const existing = this.items.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...itemData };
    this.items.set(id, updated);
    return updated;
  }

  async deleteItem(id: string): Promise<boolean> {
    return this.items.delete(id);
  }

  // Worker methods
  async getWorkers(): Promise<Worker[]> {
    return Array.from(this.workers.values());
  }

  async getWorker(id: string): Promise<Worker | undefined> {
    return this.workers.get(id);
  }

  async createWorker(insertWorker: InsertWorker): Promise<Worker> {
    const id = insertWorker.id || this.generateId('worker');
    const worker: Worker = { 
      ...insertWorker, 
      id,
      createdAt: new Date()
    };
    this.workers.set(id, worker);
    return worker;
  }

  async updateWorker(id: string, workerData: Partial<InsertWorker>): Promise<Worker | undefined> {
    const existing = this.workers.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...workerData };
    this.workers.set(id, updated);
    return updated;
  }

  async deleteWorker(id: string): Promise<boolean> {
    return this.workers.delete(id);
  }

  // Metal methods
  async getMetals(): Promise<Metal[]> {
    return Array.from(this.metals.values());
  }

  async getMetal(id: string): Promise<Metal | undefined> {
    return this.metals.get(id);
  }

  async createMetal(insertMetal: InsertMetal): Promise<Metal> {
    const id = insertMetal.id || this.generateId('metal');
    const metal: Metal = { 
      ...insertMetal, 
      id,
      createdAt: new Date()
    };
    this.metals.set(id, metal);
    return metal;
  }

  async updateMetal(id: string, metalData: Partial<InsertMetal>): Promise<Metal | undefined> {
    const existing = this.metals.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...metalData };
    this.metals.set(id, updated);
    return updated;
  }

  async deleteMetal(id: string): Promise<boolean> {
    return this.metals.delete(id);
  }

  // Purity methods
  async getPurities(): Promise<Purity[]> {
    return Array.from(this.purities.values());
  }

  async getPurity(id: string): Promise<Purity | undefined> {
    return this.purities.get(id);
  }

  async createPurity(insertPurity: InsertPurity): Promise<Purity> {
    const id = insertPurity.id || this.generateId('purity');
    const purity: Purity = { 
      ...insertPurity, 
      id,
      createdAt: new Date()
    };
    this.purities.set(id, purity);
    return purity;
  }

  async updatePurity(id: string, purityData: Partial<InsertPurity>): Promise<Purity | undefined> {
    const existing = this.purities.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...purityData };
    this.purities.set(id, updated);
    return updated;
  }

  async deletePurity(id: string): Promise<boolean> {
    return this.purities.delete(id);
  }

  // Item Inward methods
  async getItemInwards(): Promise<ItemInward[]> {
    return Array.from(this.itemInwards.values());
  }

  async getItemInward(id: string): Promise<ItemInward | undefined> {
    return this.itemInwards.get(id);
  }

  async createItemInward(insertItemInward: InsertItemInward): Promise<ItemInward> {
    const id = insertItemInward.id || this.generateId('itemInward');
    const itemInward: ItemInward = { 
      ...insertItemInward, 
      id,
      createdAt: new Date()
    };
    this.itemInwards.set(id, itemInward);
    return itemInward;
  }

  async updateItemInward(id: string, itemInwardData: Partial<InsertItemInward>): Promise<ItemInward | undefined> {
    const existing = this.itemInwards.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...itemInwardData };
    this.itemInwards.set(id, updated);
    return updated;
  }

  async deleteItemInward(id: string): Promise<boolean> {
    return this.itemInwards.delete(id);
  }

  // Item Outward methods
  async getItemOutwards(): Promise<ItemOutward[]> {
    return Array.from(this.itemOutwards.values());
  }

  async getItemOutward(id: string): Promise<ItemOutward | undefined> {
    return this.itemOutwards.get(id);
  }

  async createItemOutward(insertItemOutward: InsertItemOutward): Promise<ItemOutward> {
    const id = insertItemOutward.id || this.generateId('itemOutward');
    const itemOutward: ItemOutward = { 
      ...insertItemOutward, 
      id,
      createdAt: new Date()
    };
    this.itemOutwards.set(id, itemOutward);
    return itemOutward;
  }

  async updateItemOutward(id: string, itemOutwardData: Partial<InsertItemOutward>): Promise<ItemOutward | undefined> {
    const existing = this.itemOutwards.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...itemOutwardData };
    this.itemOutwards.set(id, updated);
    return updated;
  }

  async deleteItemOutward(id: string): Promise<boolean> {
    return this.itemOutwards.delete(id);
  }

  // Metal Inward methods
  async getMetalInwards(): Promise<MetalInward[]> {
    return Array.from(this.metalInwards.values());
  }

  async getMetalInward(id: string): Promise<MetalInward | undefined> {
    return this.metalInwards.get(id);
  }

  async createMetalInward(insertMetalInward: InsertMetalInward): Promise<MetalInward> {
    const id = insertMetalInward.id || this.generateId('metalInward');
    const metalInward: MetalInward = { 
      ...insertMetalInward, 
      id,
      createdAt: new Date()
    };
    this.metalInwards.set(id, metalInward);
    return metalInward;
  }

  async updateMetalInward(id: string, metalInwardData: Partial<InsertMetalInward>): Promise<MetalInward | undefined> {
    const existing = this.metalInwards.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...metalInwardData };
    this.metalInwards.set(id, updated);
    return updated;
  }

  async deleteMetalInward(id: string): Promise<boolean> {
    return this.metalInwards.delete(id);
  }

  // Metal Outward methods
  async getMetalOutwards(): Promise<MetalOutward[]> {
    return Array.from(this.metalOutwards.values());
  }

  async getMetalOutward(id: string): Promise<MetalOutward | undefined> {
    return this.metalOutwards.get(id);
  }

  async createMetalOutward(insertMetalOutward: InsertMetalOutward): Promise<MetalOutward> {
    const id = insertMetalOutward.id || this.generateId('metalOutward');
    const metalOutward: MetalOutward = {
      ...insertMetalOutward,
      id,
      createdAt: new Date(),
      createdBy: insertMetalOutward.createdBy || "<EMAIL>",
      status: insertMetalOutward.status || "Active",
      updatedAt: null,
      updatedBy: null
    };
    this.metalOutwards.set(id, metalOutward);
    return metalOutward;
  }

  async updateMetalOutward(id: string, metalOutwardData: Partial<InsertMetalOutward>): Promise<MetalOutward | undefined> {
    const existing = this.metalOutwards.get(id);
    if (!existing) return undefined;

    const updated = {
      ...existing,
      ...metalOutwardData,
      updatedAt: new Date(),
      updatedBy: "<EMAIL>"
    };
    this.metalOutwards.set(id, updated);
    return updated;
  }

  async deleteMetalOutward(id: string): Promise<boolean> {
    return this.metalOutwards.delete(id);
  }

  // Sales methods
  async getSales(): Promise<Sales[]> {
    return Array.from(this.salesData.values());
  }

  async getSale(id: string): Promise<Sales | undefined> {
    return this.salesData.get(id);
  }

  async createSale(insertSale: InsertSales): Promise<Sales> {
    const id = insertSale.id || this.generateId('sales');
    const sale: Sales = { 
      ...insertSale, 
      id,
      createdAt: new Date()
    };
    this.salesData.set(id, sale);
    return sale;
  }

  async updateSale(id: string, saleData: Partial<InsertSales>): Promise<Sales | undefined> {
    const existing = this.salesData.get(id);
    if (!existing) return undefined;
    
    const updated = { ...existing, ...saleData };
    this.salesData.set(id, updated);
    return updated;
  }

  async deleteSale(id: string): Promise<boolean> {
    return this.salesData.delete(id);
  }
}

export const storage = new MemStorage();
