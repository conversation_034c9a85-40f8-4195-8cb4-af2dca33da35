import mysql from 'mysql2/promise';

// Database configuration
export const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'jewelry123',
  database: 'jewelry_tracker',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Create connection pool
export const pool = mysql.createPool(dbConfig);

// Test database connection
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.log('❌ Database connection failed - MySQL not running');
    return false;
  }
}

// Initialize database tables
export async function initializeDatabase() {
  try {
    const connected = await testConnection();
    if (!connected) {
      console.log('⚠️  Skipping database initialization - MySQL not available');
      return false;
    }

    // Create tables if they don't exist
    await createTables();

    console.log('✅ Database initialized successfully');
    return true;
  } catch (error) {
    console.log('❌ Database initialization failed:', error.message);
    return false;
  }
}

async function createTables() {
  const connection = await pool.getConnection();
  
  try {
    // Customers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS customers (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        balance DECIMAL(15,2) DEFAULT 0.00,
        mobile VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        remarks TEXT,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255)
      )
    `);

    // Items table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS items (
        id VARCHAR(50) PRIMARY KEY,
        number VARCHAR(100) NOT NULL UNIQUE,
        description TEXT NOT NULL,
        remarks TEXT,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255)
      )
    `);

    // Workers table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS workers (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        balance DECIMAL(15,2) DEFAULT 0.00,
        remarks TEXT,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255)
      )
    `);

    // Metals table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS metals (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255)
      )
    `);

    // Purities table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS purities (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        percentage DECIMAL(5,2),
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255)
      )
    `);

    // Item Inwards table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS item_inwards (
        id VARCHAR(50) PRIMARY KEY,
        date DATE NOT NULL,
        customer_id VARCHAR(50) NOT NULL,
        item_id VARCHAR(50) NOT NULL,
        melting VARCHAR(100),
        gross_weight DECIMAL(10,3),
        st VARCHAR(100),
        en VARCHAR(100),
        thd VARCHAR(100),
        piece_count INT,
        net_weight DECIMAL(10,3),
        comments TEXT,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255),
        FOREIGN KEY (customer_id) REFERENCES customers(id),
        FOREIGN KEY (item_id) REFERENCES items(id)
      )
    `);

    // Sales table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS sales (
        id VARCHAR(50) PRIMARY KEY,
        date DATE NOT NULL,
        customer_id VARCHAR(50) NOT NULL,
        item_id VARCHAR(50) NOT NULL,
        amount DECIMAL(15,2),
        net_weight DECIMAL(10,3),
        remarks TEXT,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(255),
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255),
        FOREIGN KEY (customer_id) REFERENCES customers(id),
        FOREIGN KEY (item_id) REFERENCES items(id)
      )
    `);

    console.log('✅ All tables created successfully');
  } finally {
    connection.release();
  }
}

// Helper function to execute queries
export async function executeQuery(query: string, params: any[] = []) {
  const connection = await pool.getConnection();
  try {
    const [results] = await connection.execute(query, params);
    return results;
  } finally {
    connection.release();
  }
}

// Helper function for transactions
export async function executeTransaction(queries: Array<{query: string, params: any[]}>) {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    
    const results = [];
    for (const {query, params} of queries) {
      const [result] = await connection.execute(query, params);
      results.push(result);
    }
    
    await connection.commit();
    return results;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}
