import type { Express } from "express";
import { createServer, type Server } from "http";
import { 
  CustomerService, 
  ItemService, 
  WorkerService, 
  MetalService, 
  PurityService, 
  ItemInwardService, 
  SalesService 
} from "./services/database-service.js";
import {
  insertCustomerSchema, insertItemSchema, insertWorkerSchema,
  insertMetalSchema, insertPuritySchema, insertItemInwardSchema,
  insertItemOutwardSchema, insertMetalInwardSchema, insertMetalOutwardSchema, insertSalesSchema
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication
  app.post("/api/auth/login", async (req, res) => {
    const { username, password } = req.body;
    
    if (username === "<EMAIL>" && password === "Admin@1234") {
      res.json({ success: true, user: { username } });
    } else {
      res.status(401).json({ success: false, message: "Invalid credentials" });
    }
  });

  // Customer routes
  app.get("/api/customers", async (req, res) => {
    try {
      const customers = await CustomerService.getAll();
      res.json(customers);
    } catch (error) {
      res.status(500).json({ message: "Error fetching customers", error });
    }
  });

  app.get("/api/customers/:id", async (req, res) => {
    try {
      const customer = await CustomerService.getById(req.params.id);
      if (!customer) {
        return res.status(404).json({ message: "Customer not found" });
      }
      res.json(customer);
    } catch (error) {
      res.status(500).json({ message: "Error fetching customer", error });
    }
  });

  app.post("/api/customers", async (req, res) => {
    try {
      const customerData = insertCustomerSchema.parse(req.body);
      const result = await CustomerService.create(customerData);
      res.status(201).json({ id: customerData.id, ...customerData });
    } catch (error) {
      res.status(400).json({ message: "Invalid customer data", error });
    }
  });

  app.put("/api/customers/:id", async (req, res) => {
    try {
      const customerData = insertCustomerSchema.partial().parse(req.body);
      const result = await CustomerService.update(req.params.id, customerData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Customer not found" });
      }
      const updatedCustomer = await CustomerService.getById(req.params.id);
      res.json(updatedCustomer);
    } catch (error) {
      res.status(400).json({ message: "Invalid customer data", error });
    }
  });

  app.delete("/api/customers/:id", async (req, res) => {
    try {
      const result = await CustomerService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Customer not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting customer", error });
    }
  });

  // Item routes
  app.get("/api/items", async (req, res) => {
    try {
      const items = await ItemService.getAll();
      res.json(items);
    } catch (error) {
      res.status(500).json({ message: "Error fetching items", error });
    }
  });

  app.get("/api/items/:id", async (req, res) => {
    try {
      const item = await ItemService.getById(req.params.id);
      if (!item) {
        return res.status(404).json({ message: "Item not found" });
      }
      res.json(item);
    } catch (error) {
      res.status(500).json({ message: "Error fetching item", error });
    }
  });

  app.post("/api/items", async (req, res) => {
    try {
      const itemData = insertItemSchema.parse(req.body);
      const result = await ItemService.create(itemData);
      res.status(201).json({ id: itemData.id, ...itemData });
    } catch (error) {
      res.status(400).json({ message: "Invalid item data", error });
    }
  });

  app.put("/api/items/:id", async (req, res) => {
    try {
      const itemData = insertItemSchema.partial().parse(req.body);
      const result = await ItemService.update(req.params.id, itemData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Item not found" });
      }
      const updatedItem = await ItemService.getById(req.params.id);
      res.json(updatedItem);
    } catch (error) {
      res.status(400).json({ message: "Invalid item data", error });
    }
  });

  app.delete("/api/items/:id", async (req, res) => {
    try {
      const result = await ItemService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Item not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting item", error });
    }
  });

  // Worker routes
  app.get("/api/workers", async (req, res) => {
    try {
      const workers = await WorkerService.getAll();
      res.json(workers);
    } catch (error) {
      res.status(500).json({ message: "Error fetching workers", error });
    }
  });

  app.post("/api/workers", async (req, res) => {
    try {
      const workerData = insertWorkerSchema.parse(req.body);
      const result = await WorkerService.create(workerData);
      res.status(201).json({ id: workerData.id, ...workerData });
    } catch (error) {
      res.status(400).json({ message: "Invalid worker data", error });
    }
  });

  app.put("/api/workers/:id", async (req, res) => {
    try {
      const workerData = insertWorkerSchema.partial().parse(req.body);
      const result = await WorkerService.update(req.params.id, workerData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Worker not found" });
      }
      const updatedWorker = await WorkerService.getById(req.params.id);
      res.json(updatedWorker);
    } catch (error) {
      res.status(400).json({ message: "Invalid worker data", error });
    }
  });

  app.delete("/api/workers/:id", async (req, res) => {
    try {
      const result = await WorkerService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Worker not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting worker", error });
    }
  });

  // Metal routes
  app.get("/api/metals", async (req, res) => {
    try {
      const metals = await MetalService.getAll();
      res.json(metals);
    } catch (error) {
      res.status(500).json({ message: "Error fetching metals", error });
    }
  });

  app.post("/api/metals", async (req, res) => {
    try {
      const metalData = insertMetalSchema.parse(req.body);
      const result = await MetalService.create(metalData);
      res.status(201).json({ id: metalData.id, ...metalData });
    } catch (error) {
      res.status(400).json({ message: "Invalid metal data", error });
    }
  });

  app.put("/api/metals/:id", async (req, res) => {
    try {
      const metalData = insertMetalSchema.partial().parse(req.body);
      const result = await MetalService.update(req.params.id, metalData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Metal not found" });
      }
      const updatedMetal = await MetalService.getById(req.params.id);
      res.json(updatedMetal);
    } catch (error) {
      res.status(400).json({ message: "Invalid metal data", error });
    }
  });

  app.delete("/api/metals/:id", async (req, res) => {
    try {
      const result = await MetalService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Metal not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting metal", error });
    }
  });

  // Purity routes
  app.get("/api/purities", async (req, res) => {
    try {
      const purities = await PurityService.getAll();
      res.json(purities);
    } catch (error) {
      res.status(500).json({ message: "Error fetching purities", error });
    }
  });

  app.post("/api/purities", async (req, res) => {
    try {
      const purityData = insertPuritySchema.parse(req.body);
      const result = await PurityService.create(purityData);
      res.status(201).json({ id: purityData.id, ...purityData });
    } catch (error) {
      res.status(400).json({ message: "Invalid purity data", error });
    }
  });

  app.put("/api/purities/:id", async (req, res) => {
    try {
      const purityData = insertPuritySchema.partial().parse(req.body);
      const result = await PurityService.update(req.params.id, purityData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Purity not found" });
      }
      const updatedPurity = await PurityService.getById(req.params.id);
      res.json(updatedPurity);
    } catch (error) {
      res.status(400).json({ message: "Invalid purity data", error });
    }
  });

  app.delete("/api/purities/:id", async (req, res) => {
    try {
      const result = await PurityService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Purity not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting purity", error });
    }
  });

  // Item Inward routes
  app.get("/api/item-inwards", async (req, res) => {
    try {
      const itemInwards = await ItemInwardService.getAll();
      res.json(itemInwards);
    } catch (error) {
      res.status(500).json({ message: "Error fetching item inwards", error });
    }
  });

  app.post("/api/item-inwards", async (req, res) => {
    try {
      const itemInwardData = insertItemInwardSchema.parse(req.body);
      const result = await ItemInwardService.create(itemInwardData);
      res.status(201).json({ id: itemInwardData.id, ...itemInwardData });
    } catch (error) {
      res.status(400).json({ message: "Invalid item inward data", error });
    }
  });

  app.put("/api/item-inwards/:id", async (req, res) => {
    try {
      const itemInwardData = insertItemInwardSchema.partial().parse(req.body);
      const result = await ItemInwardService.update(req.params.id, itemInwardData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Item inward not found" });
      }
      const updatedItemInward = await ItemInwardService.getById(req.params.id);
      res.json(updatedItemInward);
    } catch (error) {
      res.status(400).json({ message: "Invalid item inward data", error });
    }
  });

  app.delete("/api/item-inwards/:id", async (req, res) => {
    try {
      const result = await ItemInwardService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Item inward not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting item inward", error });
    }
  });

  // Sales routes
  app.get("/api/sales", async (req, res) => {
    try {
      const sales = await SalesService.getAll();
      res.json(sales);
    } catch (error) {
      res.status(500).json({ message: "Error fetching sales", error });
    }
  });

  app.post("/api/sales", async (req, res) => {
    try {
      const salesData = insertSalesSchema.parse(req.body);
      const result = await SalesService.create(salesData);
      res.status(201).json({ id: salesData.id, ...salesData });
    } catch (error) {
      res.status(400).json({ message: "Invalid sales data", error });
    }
  });

  app.put("/api/sales/:id", async (req, res) => {
    try {
      const salesData = insertSalesSchema.partial().parse(req.body);
      const result = await SalesService.update(req.params.id, salesData);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Sale not found" });
      }
      const updatedSale = await SalesService.getById(req.params.id);
      res.json(updatedSale);
    } catch (error) {
      res.status(400).json({ message: "Invalid sales data", error });
    }
  });

  app.delete("/api/sales/:id", async (req, res) => {
    try {
      const result = await SalesService.delete(req.params.id);
      if (result.affectedRows === 0) {
        return res.status(404).json({ message: "Sale not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Error deleting sale", error });
    }
  });

  // Placeholder routes for other transactions (to be implemented)
  app.get("/api/item-outwards", async (req, res) => {
    res.json([]);
  });

  app.get("/api/metal-inwards", async (req, res) => {
    res.json([]);
  });

  app.get("/api/metal-outwards", async (req, res) => {
    res.json([]);
  });

  const httpServer = createServer(app);
  return httpServer;
}
