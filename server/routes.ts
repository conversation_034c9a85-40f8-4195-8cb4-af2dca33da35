import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { 
  insertCustomerSchema, insertItemSchema, insertWorkerSchema, 
  insertMetalSchema, insertPuritySchema, insertItemInwardSchema,
  insertItemOutwardSchema, insertMetalInwardSchema, insertSalesSchema
} from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication
  app.post("/api/auth/login", async (req, res) => {
    const { username, password } = req.body;
    
    if (username === "<EMAIL>" && password === "Admin@1234") {
      res.json({ success: true, user: { username } });
    } else {
      res.status(401).json({ success: false, message: "Invalid credentials" });
    }
  });

  // Customer routes
  app.get("/api/customers", async (req, res) => {
    const customers = await storage.getCustomers();
    res.json(customers);
  });

  app.get("/api/customers/:id", async (req, res) => {
    const customer = await storage.getCustomer(req.params.id);
    if (!customer) {
      return res.status(404).json({ message: "Customer not found" });
    }
    res.json(customer);
  });

  app.post("/api/customers", async (req, res) => {
    try {
      const customerData = insertCustomerSchema.parse(req.body);
      const customer = await storage.createCustomer(customerData);
      res.status(201).json(customer);
    } catch (error) {
      res.status(400).json({ message: "Invalid customer data", error });
    }
  });

  app.put("/api/customers/:id", async (req, res) => {
    try {
      const customerData = insertCustomerSchema.partial().parse(req.body);
      const customer = await storage.updateCustomer(req.params.id, customerData);
      if (!customer) {
        return res.status(404).json({ message: "Customer not found" });
      }
      res.json(customer);
    } catch (error) {
      res.status(400).json({ message: "Invalid customer data", error });
    }
  });

  app.delete("/api/customers/:id", async (req, res) => {
    const deleted = await storage.deleteCustomer(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Customer not found" });
    }
    res.json({ success: true });
  });

  // Item routes
  app.get("/api/items", async (req, res) => {
    const items = await storage.getItems();
    res.json(items);
  });

  app.get("/api/items/:id", async (req, res) => {
    const item = await storage.getItem(req.params.id);
    if (!item) {
      return res.status(404).json({ message: "Item not found" });
    }
    res.json(item);
  });

  app.post("/api/items", async (req, res) => {
    try {
      const itemData = insertItemSchema.parse(req.body);
      const item = await storage.createItem(itemData);
      res.status(201).json(item);
    } catch (error) {
      res.status(400).json({ message: "Invalid item data", error });
    }
  });

  app.put("/api/items/:id", async (req, res) => {
    try {
      const itemData = insertItemSchema.partial().parse(req.body);
      const item = await storage.updateItem(req.params.id, itemData);
      if (!item) {
        return res.status(404).json({ message: "Item not found" });
      }
      res.json(item);
    } catch (error) {
      res.status(400).json({ message: "Invalid item data", error });
    }
  });

  app.delete("/api/items/:id", async (req, res) => {
    const deleted = await storage.deleteItem(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Item not found" });
    }
    res.json({ success: true });
  });

  // Worker routes
  app.get("/api/workers", async (req, res) => {
    const workers = await storage.getWorkers();
    res.json(workers);
  });

  app.post("/api/workers", async (req, res) => {
    try {
      const workerData = insertWorkerSchema.parse(req.body);
      const worker = await storage.createWorker(workerData);
      res.status(201).json(worker);
    } catch (error) {
      res.status(400).json({ message: "Invalid worker data", error });
    }
  });

  app.put("/api/workers/:id", async (req, res) => {
    try {
      const workerData = insertWorkerSchema.partial().parse(req.body);
      const worker = await storage.updateWorker(req.params.id, workerData);
      if (!worker) {
        return res.status(404).json({ message: "Worker not found" });
      }
      res.json(worker);
    } catch (error) {
      res.status(400).json({ message: "Invalid worker data", error });
    }
  });

  app.delete("/api/workers/:id", async (req, res) => {
    const deleted = await storage.deleteWorker(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Worker not found" });
    }
    res.json({ success: true });
  });

  // Metal routes
  app.get("/api/metals", async (req, res) => {
    const metals = await storage.getMetals();
    res.json(metals);
  });

  app.post("/api/metals", async (req, res) => {
    try {
      const metalData = insertMetalSchema.parse(req.body);
      const metal = await storage.createMetal(metalData);
      res.status(201).json(metal);
    } catch (error) {
      res.status(400).json({ message: "Invalid metal data", error });
    }
  });

  app.put("/api/metals/:id", async (req, res) => {
    try {
      const metalData = insertMetalSchema.partial().parse(req.body);
      const metal = await storage.updateMetal(req.params.id, metalData);
      if (!metal) {
        return res.status(404).json({ message: "Metal not found" });
      }
      res.json(metal);
    } catch (error) {
      res.status(400).json({ message: "Invalid metal data", error });
    }
  });

  app.delete("/api/metals/:id", async (req, res) => {
    const deleted = await storage.deleteMetal(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Metal not found" });
    }
    res.json({ success: true });
  });

  // Purity routes
  app.get("/api/purities", async (req, res) => {
    const purities = await storage.getPurities();
    res.json(purities);
  });

  app.post("/api/purities", async (req, res) => {
    try {
      const purityData = insertPuritySchema.parse(req.body);
      const purity = await storage.createPurity(purityData);
      res.status(201).json(purity);
    } catch (error) {
      res.status(400).json({ message: "Invalid purity data", error });
    }
  });

  app.put("/api/purities/:id", async (req, res) => {
    try {
      const purityData = insertPuritySchema.partial().parse(req.body);
      const purity = await storage.updatePurity(req.params.id, purityData);
      if (!purity) {
        return res.status(404).json({ message: "Purity not found" });
      }
      res.json(purity);
    } catch (error) {
      res.status(400).json({ message: "Invalid purity data", error });
    }
  });

  app.delete("/api/purities/:id", async (req, res) => {
    const deleted = await storage.deletePurity(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Purity not found" });
    }
    res.json({ success: true });
  });

  // Item Inward routes
  app.get("/api/item-inwards", async (req, res) => {
    const itemInwards = await storage.getItemInwards();
    res.json(itemInwards);
  });

  app.post("/api/item-inwards", async (req, res) => {
    try {
      const itemInwardData = insertItemInwardSchema.parse(req.body);
      const itemInward = await storage.createItemInward(itemInwardData);
      res.status(201).json(itemInward);
    } catch (error) {
      res.status(400).json({ message: "Invalid item inward data", error });
    }
  });

  app.put("/api/item-inwards/:id", async (req, res) => {
    try {
      const itemInwardData = insertItemInwardSchema.partial().parse(req.body);
      const itemInward = await storage.updateItemInward(req.params.id, itemInwardData);
      if (!itemInward) {
        return res.status(404).json({ message: "Item inward not found" });
      }
      res.json(itemInward);
    } catch (error) {
      res.status(400).json({ message: "Invalid item inward data", error });
    }
  });

  app.delete("/api/item-inwards/:id", async (req, res) => {
    const deleted = await storage.deleteItemInward(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Item inward not found" });
    }
    res.json({ success: true });
  });

  // Item Outward routes
  app.get("/api/item-outwards", async (req, res) => {
    const itemOutwards = await storage.getItemOutwards();
    res.json(itemOutwards);
  });

  app.post("/api/item-outwards", async (req, res) => {
    try {
      const itemOutwardData = insertItemOutwardSchema.parse(req.body);
      const itemOutward = await storage.createItemOutward(itemOutwardData);
      res.status(201).json(itemOutward);
    } catch (error) {
      res.status(400).json({ message: "Invalid item outward data", error });
    }
  });

  app.put("/api/item-outwards/:id", async (req, res) => {
    try {
      const itemOutwardData = insertItemOutwardSchema.partial().parse(req.body);
      const itemOutward = await storage.updateItemOutward(req.params.id, itemOutwardData);
      if (!itemOutward) {
        return res.status(404).json({ message: "Item outward not found" });
      }
      res.json(itemOutward);
    } catch (error) {
      res.status(400).json({ message: "Invalid item outward data", error });
    }
  });

  app.delete("/api/item-outwards/:id", async (req, res) => {
    const deleted = await storage.deleteItemOutward(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Item outward not found" });
    }
    res.json({ success: true });
  });

  // Metal Inward routes
  app.get("/api/metal-inwards", async (req, res) => {
    const metalInwards = await storage.getMetalInwards();
    res.json(metalInwards);
  });

  app.post("/api/metal-inwards", async (req, res) => {
    try {
      const metalInwardData = insertMetalInwardSchema.parse(req.body);
      const metalInward = await storage.createMetalInward(metalInwardData);
      res.status(201).json(metalInward);
    } catch (error) {
      res.status(400).json({ message: "Invalid metal inward data", error });
    }
  });

  app.put("/api/metal-inwards/:id", async (req, res) => {
    try {
      const metalInwardData = insertMetalInwardSchema.partial().parse(req.body);
      const metalInward = await storage.updateMetalInward(req.params.id, metalInwardData);
      if (!metalInward) {
        return res.status(404).json({ message: "Metal inward not found" });
      }
      res.json(metalInward);
    } catch (error) {
      res.status(400).json({ message: "Invalid metal inward data", error });
    }
  });

  app.delete("/api/metal-inwards/:id", async (req, res) => {
    const deleted = await storage.deleteMetalInward(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Metal inward not found" });
    }
    res.json({ success: true });
  });

  // Sales routes
  app.get("/api/sales", async (req, res) => {
    const sales = await storage.getSales();
    res.json(sales);
  });

  app.post("/api/sales", async (req, res) => {
    try {
      const salesData = insertSalesSchema.parse(req.body);
      const sale = await storage.createSale(salesData);
      res.status(201).json(sale);
    } catch (error) {
      res.status(400).json({ message: "Invalid sales data", error });
    }
  });

  app.put("/api/sales/:id", async (req, res) => {
    try {
      const salesData = insertSalesSchema.partial().parse(req.body);
      const sale = await storage.updateSale(req.params.id, salesData);
      if (!sale) {
        return res.status(404).json({ message: "Sale not found" });
      }
      res.json(sale);
    } catch (error) {
      res.status(400).json({ message: "Invalid sales data", error });
    }
  });

  app.delete("/api/sales/:id", async (req, res) => {
    const deleted = await storage.deleteSale(req.params.id);
    if (!deleted) {
      return res.status(404).json({ message: "Sale not found" });
    }
    res.json({ success: true });
  });

  const httpServer = createServer(app);
  return httpServer;
}
