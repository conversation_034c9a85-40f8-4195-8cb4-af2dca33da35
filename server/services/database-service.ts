import { executeQuery, executeTransaction } from '../database.js';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

// Customer Service
export class CustomerService {
  static async getAll() {
    const query = 'SELECT * FROM customers ORDER BY created_at DESC';
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = 'SELECT * FROM customers WHERE id = ?';
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(customer: any) {
    const query = `
      INSERT INTO customers (id, name, balance, mobile, email, address, remarks, status, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      customer.id,
      customer.name,
      customer.balance || 0,
      customer.mobile,
      customer.email,
      customer.address,
      customer.remarks,
      customer.status || 'Active',
      customer.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, customer: any) {
    const query = `
      UPDATE customers 
      SET name = ?, balance = ?, mobile = ?, email = ?, address = ?, remarks = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      customer.name,
      customer.balance || 0,
      customer.mobile,
      customer.email,
      customer.address,
      customer.remarks,
      customer.status || 'Active',
      customer.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM customers WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}

// Item Service
export class ItemService {
  static async getAll() {
    const query = 'SELECT * FROM items ORDER BY created_at DESC';
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = 'SELECT * FROM items WHERE id = ?';
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(item: any) {
    const query = `
      INSERT INTO items (id, number, description, remarks, status, created_by)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    const params = [
      item.id,
      item.number,
      item.description,
      item.remarks,
      item.status || 'Active',
      item.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, item: any) {
    const query = `
      UPDATE items 
      SET number = ?, description = ?, remarks = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      item.number,
      item.description,
      item.remarks,
      item.status || 'Active',
      item.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM items WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}

// Worker Service
export class WorkerService {
  static async getAll() {
    const query = 'SELECT * FROM workers ORDER BY created_at DESC';
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = 'SELECT * FROM workers WHERE id = ?';
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(worker: any) {
    const query = `
      INSERT INTO workers (id, name, balance, remarks, status, created_by)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    const params = [
      worker.id,
      worker.name,
      worker.balance || 0,
      worker.remarks,
      worker.status || 'Active',
      worker.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, worker: any) {
    const query = `
      UPDATE workers 
      SET name = ?, balance = ?, remarks = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      worker.name,
      worker.balance || 0,
      worker.remarks,
      worker.status || 'Active',
      worker.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM workers WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}

// Metal Service
export class MetalService {
  static async getAll() {
    const query = 'SELECT * FROM metals ORDER BY created_at DESC';
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = 'SELECT * FROM metals WHERE id = ?';
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(metal: any) {
    const query = `
      INSERT INTO metals (id, name, description, status, created_by)
      VALUES (?, ?, ?, ?, ?)
    `;
    const params = [
      metal.id,
      metal.name,
      metal.description,
      metal.status || 'Active',
      metal.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, metal: any) {
    const query = `
      UPDATE metals 
      SET name = ?, description = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      metal.name,
      metal.description,
      metal.status || 'Active',
      metal.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM metals WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}

// Purity Service
export class PurityService {
  static async getAll() {
    const query = 'SELECT * FROM purities ORDER BY created_at DESC';
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = 'SELECT * FROM purities WHERE id = ?';
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(purity: any) {
    const query = `
      INSERT INTO purities (id, name, percentage, status, created_by)
      VALUES (?, ?, ?, ?, ?)
    `;
    const params = [
      purity.id,
      purity.name,
      purity.percentage,
      purity.status || 'Active',
      purity.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, purity: any) {
    const query = `
      UPDATE purities 
      SET name = ?, percentage = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      purity.name,
      purity.percentage,
      purity.status || 'Active',
      purity.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM purities WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}

// Item Inward Service
export class ItemInwardService {
  static async getAll() {
    const query = `
      SELECT ii.*, c.name as customer_name, i.description as item_description
      FROM item_inwards ii
      LEFT JOIN customers c ON ii.customer_id = c.id
      LEFT JOIN items i ON ii.item_id = i.id
      ORDER BY ii.created_at DESC
    `;
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = `
      SELECT ii.*, c.name as customer_name, i.description as item_description
      FROM item_inwards ii
      LEFT JOIN customers c ON ii.customer_id = c.id
      LEFT JOIN items i ON ii.item_id = i.id
      WHERE ii.id = ?
    `;
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(inward: any) {
    const query = `
      INSERT INTO item_inwards (id, date, customer_id, item_id, melting, gross_weight, st, en, thd, piece_count, net_weight, comments, status, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      inward.id,
      inward.date,
      inward.customerId,
      inward.itemId,
      inward.melting,
      inward.grossWeight,
      inward.st,
      inward.en,
      inward.thd,
      inward.pieceCount,
      inward.netWeight,
      inward.comments,
      inward.status || 'Active',
      inward.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, inward: any) {
    const query = `
      UPDATE item_inwards
      SET date = ?, customer_id = ?, item_id = ?, melting = ?, gross_weight = ?, st = ?, en = ?, thd = ?, piece_count = ?, net_weight = ?, comments = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      inward.date,
      inward.customerId,
      inward.itemId,
      inward.melting,
      inward.grossWeight,
      inward.st,
      inward.en,
      inward.thd,
      inward.pieceCount,
      inward.netWeight,
      inward.comments,
      inward.status || 'Active',
      inward.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM item_inwards WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}

// Sales Service
export class SalesService {
  static async getAll() {
    const query = `
      SELECT s.*, c.name as customer_name, i.description as item_description
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN items i ON s.item_id = i.id
      ORDER BY s.created_at DESC
    `;
    return await executeQuery(query) as RowDataPacket[];
  }

  static async getById(id: string) {
    const query = `
      SELECT s.*, c.name as customer_name, i.description as item_description
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN items i ON s.item_id = i.id
      WHERE s.id = ?
    `;
    const results = await executeQuery(query, [id]) as RowDataPacket[];
    return results[0] || null;
  }

  static async create(sale: any) {
    const query = `
      INSERT INTO sales (id, date, customer_id, item_id, amount, net_weight, remarks, status, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      sale.id,
      sale.date,
      sale.customerId,
      sale.itemId,
      sale.amount,
      sale.netWeight,
      sale.remarks,
      sale.status || 'Active',
      sale.createdBy || '<EMAIL>'
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async update(id: string, sale: any) {
    const query = `
      UPDATE sales
      SET date = ?, customer_id = ?, item_id = ?, amount = ?, net_weight = ?, remarks = ?, status = ?, updated_by = ?
      WHERE id = ?
    `;
    const params = [
      sale.date,
      sale.customerId,
      sale.itemId,
      sale.amount,
      sale.netWeight,
      sale.remarks,
      sale.status || 'Active',
      sale.updatedBy || '<EMAIL>',
      id
    ];
    return await executeQuery(query, params) as ResultSetHeader;
  }

  static async delete(id: string) {
    const query = 'DELETE FROM sales WHERE id = ?';
    return await executeQuery(query, [id]) as ResultSetHeader;
  }
}
