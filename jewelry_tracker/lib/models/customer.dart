class Customer {
  final String id;
  final String name;
  final double balance;
  final String? mobile;
  final String? email;
  final String? address;
  final String? remarks;
  final String status;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;

  Customer({
    required this.id,
    required this.name,
    required this.balance,
    this.mobile,
    this.email,
    this.address,
    this.remarks,
    required this.status,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  factory Customer.fromJson(Map<String, dynamic> json) {
    return Customer(
      id: json['id'],
      name: json['name'],
      balance: double.tryParse(json['balance'].toString()) ?? 0.0,
      mobile: json['mobile'],
      email: json['email'],
      address: json['address'],
      remarks: json['remarks'],
      status: json['status'] ?? 'Active',
      createdAt: DateTime.parse(json['createdAt']),
      createdBy: json['createdBy'],
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      updatedBy: json['updatedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'balance': balance.toString(),
      'mobile': mobile,
      'email': email,
      'address': address,
      'remarks': remarks,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedAt': updatedAt?.toIso8601String(),
      'updatedBy': updatedBy,
    };
  }

  Customer copyWith({
    String? id,
    String? name,
    double? balance,
    String? mobile,
    String? email,
    String? address,
    String? remarks,
    String? status,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return Customer(
      id: id ?? this.id,
      name: name ?? this.name,
      balance: balance ?? this.balance,
      mobile: mobile ?? this.mobile,
      email: email ?? this.email,
      address: address ?? this.address,
      remarks: remarks ?? this.remarks,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}
