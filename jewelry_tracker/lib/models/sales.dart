class Sales {
  final String id;
  final DateTime date;
  final String customerId;
  final String itemId;
  final String? melting;
  final double? grossWeight;
  final String? st;
  final String? en;
  final String? thd;
  final int? pieceCount;
  final double? netWeight;
  final double? amount;
  final String? comments;
  final String status;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;

  Sales({
    required this.id,
    required this.date,
    required this.customerId,
    required this.itemId,
    this.melting,
    this.grossWeight,
    this.st,
    this.en,
    this.thd,
    this.pieceCount,
    this.netWeight,
    this.amount,
    this.comments,
    required this.status,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  factory Sales.fromJson(Map<String, dynamic> json) {
    return Sales(
      id: json['id'],
      date: DateTime.parse(json['date']),
      customerId: json['customerId'],
      itemId: json['itemId'],
      melting: json['melting'],
      grossWeight: json['grossWeight'] != null ? double.tryParse(json['grossWeight'].toString()) : null,
      st: json['st'],
      en: json['en'],
      thd: json['thd'],
      pieceCount: json['pieceCount'],
      netWeight: json['netWeight'] != null ? double.tryParse(json['netWeight'].toString()) : null,
      amount: json['amount'] != null ? double.tryParse(json['amount'].toString()) : null,
      comments: json['comments'],
      status: json['status'] ?? 'Active',
      createdAt: DateTime.parse(json['createdAt']),
      createdBy: json['createdBy'],
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      updatedBy: json['updatedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String().split('T')[0],
      'customerId': customerId,
      'itemId': itemId,
      'melting': melting,
      'grossWeight': grossWeight?.toString(),
      'st': st,
      'en': en,
      'thd': thd,
      'pieceCount': pieceCount,
      'netWeight': netWeight?.toString(),
      'amount': amount?.toString(),
      'comments': comments,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedAt': updatedAt?.toIso8601String(),
      'updatedBy': updatedBy,
    };
  }

  Sales copyWith({
    String? id,
    DateTime? date,
    String? customerId,
    String? itemId,
    String? melting,
    double? grossWeight,
    String? st,
    String? en,
    String? thd,
    int? pieceCount,
    double? netWeight,
    double? amount,
    String? comments,
    String? status,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return Sales(
      id: id ?? this.id,
      date: date ?? this.date,
      customerId: customerId ?? this.customerId,
      itemId: itemId ?? this.itemId,
      melting: melting ?? this.melting,
      grossWeight: grossWeight ?? this.grossWeight,
      st: st ?? this.st,
      en: en ?? this.en,
      thd: thd ?? this.thd,
      pieceCount: pieceCount ?? this.pieceCount,
      netWeight: netWeight ?? this.netWeight,
      amount: amount ?? this.amount,
      comments: comments ?? this.comments,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}
