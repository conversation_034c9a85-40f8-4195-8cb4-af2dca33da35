class Purity {
  final String id;
  final String name;
  final String metalType;
  final String? description;
  final String? remarks;
  final String status;
  final DateTime createdAt;
  final String createdBy;
  final DateTime? updatedAt;
  final String? updatedBy;

  Purity({
    required this.id,
    required this.name,
    required this.metalType,
    this.description,
    this.remarks,
    required this.status,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  factory Purity.fromJson(Map<String, dynamic> json) {
    return Purity(
      id: json['id'],
      name: json['name'],
      metalType: json['metalType'],
      description: json['description'],
      remarks: json['remarks'],
      status: json['status'] ?? 'Active',
      createdAt: DateTime.parse(json['createdAt']),
      createdBy: json['createdBy'],
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      updatedBy: json['updatedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'metalType': metalType,
      'description': description,
      'remarks': remarks,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'createdBy': createdBy,
      'updatedAt': updatedAt?.toIso8601String(),
      'updatedBy': updatedBy,
    };
  }

  Purity copyWith({
    String? id,
    String? name,
    String? metalType,
    String? description,
    String? remarks,
    String? status,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return Purity(
      id: id ?? this.id,
      name: name ?? this.name,
      metalType: metalType ?? this.metalType,
      description: description ?? this.description,
      remarks: remarks ?? this.remarks,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}
