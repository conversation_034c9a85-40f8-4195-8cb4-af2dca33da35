import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/data_provider.dart';
import '../widgets/layout/app_layout.dart';
import '../widgets/dashboard/stats_card.dart';
import '../widgets/dashboard/quick_action_card.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DataProvider>(context, listen: false).loadAllData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppLayout(
      title: 'Dashboard',
      child: Consumer<DataProvider>(
        builder: (context, dataProvider, child) {
          if (dataProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.dashboard,
                          size: 48,
                          color: Colors.blue.shade700,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Welcome to Jewelry Tracker',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Comprehensive Inventory Management System',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Today: ${DateFormat('EEEE, MMMM d, y').format(DateTime.now())}',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Statistics Cards
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 4,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.5,
                  children: [
                    StatsCard(
                      title: 'Total Items',
                      value: dataProvider.totalItems.toString(),
                      icon: Icons.inventory,
                      color: Colors.blue,
                    ),
                    StatsCard(
                      title: 'Active Customers',
                      value: dataProvider.activeCustomers.toString(),
                      icon: Icons.people,
                      color: Colors.green,
                    ),
                    StatsCard(
                      title: 'Total Workers',
                      value: dataProvider.totalWorkers.toString(),
                      icon: Icons.engineering,
                      color: Colors.orange,
                    ),
                    StatsCard(
                      title: 'Total Sales',
                      value: '₹${NumberFormat('#,##,###').format(dataProvider.totalSalesAmount)}',
                      icon: Icons.trending_up,
                      color: Colors.purple,
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Quick Actions
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Quick Actions',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        GridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 3,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          childAspectRatio: 2,
                          children: [
                            QuickActionCard(
                              title: 'Add Customer',
                              icon: Icons.person_add,
                              color: Colors.blue,
                              onTap: () => Navigator.of(context).pushNamed('/masters'),
                            ),
                            QuickActionCard(
                              title: 'Add Item',
                              icon: Icons.add_box,
                              color: Colors.green,
                              onTap: () => Navigator.of(context).pushNamed('/masters'),
                            ),
                            QuickActionCard(
                              title: 'Add Worker',
                              icon: Icons.person_add_alt,
                              color: Colors.orange,
                              onTap: () => Navigator.of(context).pushNamed('/masters'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Recent Activity
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Recent Item Inwards
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Recent Item Inwards',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              if (dataProvider.itemInwards.isEmpty)
                                const Text('No recent item inwards')
                              else
                                ...dataProvider.itemInwards.take(5).map((inward) {
                                  final customer = dataProvider.getCustomerById(inward.customerId);
                                  final item = dataProvider.getItemById(inward.itemId);
                                  return ListTile(
                                    leading: const Icon(Icons.arrow_downward, color: Colors.green),
                                    title: Text(item?.description ?? 'Unknown Item'),
                                    subtitle: Text(customer?.name ?? 'Unknown Customer'),
                                    trailing: Text(DateFormat('MMM d').format(inward.date)),
                                  );
                                }),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    
                    // Recent Sales
                    Expanded(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(24.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Recent Sales',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              if (dataProvider.sales.isEmpty)
                                const Text('No recent sales')
                              else
                                ...dataProvider.sales.take(5).map((sale) {
                                  final customer = dataProvider.getCustomerById(sale.customerId);
                                  final item = dataProvider.getItemById(sale.itemId);
                                  return ListTile(
                                    leading: const Icon(Icons.shopping_cart, color: Colors.blue),
                                    title: Text(item?.description ?? 'Unknown Item'),
                                    subtitle: Text(customer?.name ?? 'Unknown Customer'),
                                    trailing: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          '₹${NumberFormat('#,##,###').format(sale.amount ?? 0)}',
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                        Text(
                                          DateFormat('MMM d').format(sale.date),
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                      ],
                                    ),
                                  );
                                }),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
