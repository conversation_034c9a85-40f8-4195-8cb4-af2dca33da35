import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/data_provider.dart';
import '../widgets/layout/app_layout.dart';
import '../widgets/masters/customers_tab.dart';
import '../widgets/masters/items_tab.dart';
import '../widgets/masters/workers_tab.dart';
import '../widgets/masters/metals_tab.dart';
import '../widgets/masters/purities_tab.dart';

class MastersScreen extends StatefulWidget {
  const MastersScreen({super.key});

  @override
  State<MastersScreen> createState() => _MastersScreenState();
}

class _MastersScreenState extends State<MastersScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DataProvider>(context, listen: false).loadAllData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppLayout(
      title: 'Masters',
      child: Column(
        children: [
          // Tab Bar
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.blue.shade700,
              unselectedLabelColor: Colors.grey.shade600,
              indicatorColor: Colors.blue.shade700,
              tabs: const [
                Tab(
                  icon: Icon(Icons.people),
                  text: 'Customers',
                ),
                Tab(
                  icon: Icon(Icons.inventory),
                  text: 'Items',
                ),
                Tab(
                  icon: Icon(Icons.engineering),
                  text: 'Workers',
                ),
                Tab(
                  icon: Icon(Icons.category),
                  text: 'Metals',
                ),
                Tab(
                  icon: Icon(Icons.grade),
                  text: 'Purities',
                ),
              ],
            ),
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                CustomersTab(),
                ItemsTab(),
                WorkersTab(),
                MetalsTab(),
                PuritiesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
