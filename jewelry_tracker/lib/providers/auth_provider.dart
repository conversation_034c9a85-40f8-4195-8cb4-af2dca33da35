import 'package:flutter/material.dart';
import '../services/data_service.dart';

class AuthProvider with ChangeNotifier {
  final DataService _dataService = DataService();
  bool _isLoggedIn = false;
  bool _isLoading = false;

  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;

  Future<void> checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();
    
    _isLoggedIn = await _dataService.isLoggedIn();
    
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> login(String username, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      final success = await _dataService.login(username, password);
      if (success) {
        _isLoggedIn = true;
      }
      
      _isLoading = false;
      notifyListeners();
      
      return success;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    await _dataService.logout();
    _isLoggedIn = false;
    notifyListeners();
  }
}
