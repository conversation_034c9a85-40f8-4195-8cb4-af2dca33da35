import 'package:flutter/material.dart';
import '../services/data_service.dart';
import '../models/customer.dart';
import '../models/item.dart';
import '../models/worker.dart';
import '../models/metal.dart';
import '../models/purity.dart';
import '../models/item_inward.dart';
import '../models/sales.dart';

class DataProvider with ChangeNotifier {
  final DataService _dataService = DataService();
  
  List<Customer> _customers = [];
  List<Item> _items = [];
  List<Worker> _workers = [];
  List<Metal> _metals = [];
  List<Purity> _purities = [];
  List<ItemInward> _itemInwards = [];
  List<Sales> _sales = [];
  
  bool _isLoading = false;

  // Getters
  List<Customer> get customers => _customers;
  List<Item> get items => _items;
  List<Worker> get workers => _workers;
  List<Metal> get metals => _metals;
  List<Purity> get purities => _purities;
  List<ItemInward> get itemInwards => _itemInwards;
  List<Sales> get sales => _sales;
  bool get isLoading => _isLoading;

  // Load all data
  Future<void> loadAllData() async {
    _isLoading = true;
    notifyListeners();

    try {
      _customers = await _dataService.getCustomers();
      _items = await _dataService.getItems();
      _workers = await _dataService.getWorkers();
      _metals = await _dataService.getMetals();
      _purities = await _dataService.getPurities();
      _itemInwards = await _dataService.getItemInwards();
      _sales = await _dataService.getSales();
    } catch (e) {
      debugPrint('Error loading data: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  // Customer operations
  Future<void> addCustomer(Customer customer) async {
    await _dataService.saveCustomer(customer);
    _customers = await _dataService.getCustomers();
    notifyListeners();
  }

  Future<void> updateCustomer(Customer customer) async {
    await _dataService.saveCustomer(customer);
    _customers = await _dataService.getCustomers();
    notifyListeners();
  }

  Future<void> deleteCustomer(String id) async {
    await _dataService.deleteCustomer(id);
    _customers = await _dataService.getCustomers();
    notifyListeners();
  }

  // Item operations
  Future<void> addItem(Item item) async {
    await _dataService.saveItem(item);
    _items = await _dataService.getItems();
    notifyListeners();
  }

  Future<void> updateItem(Item item) async {
    await _dataService.saveItem(item);
    _items = await _dataService.getItems();
    notifyListeners();
  }

  Future<void> deleteItem(String id) async {
    await _dataService.deleteItem(id);
    _items = await _dataService.getItems();
    notifyListeners();
  }

  // Worker operations
  Future<void> addWorker(Worker worker) async {
    await _dataService.saveWorker(worker);
    _workers = await _dataService.getWorkers();
    notifyListeners();
  }

  Future<void> updateWorker(Worker worker) async {
    await _dataService.saveWorker(worker);
    _workers = await _dataService.getWorkers();
    notifyListeners();
  }

  Future<void> deleteWorker(String id) async {
    await _dataService.deleteWorker(id);
    _workers = await _dataService.getWorkers();
    notifyListeners();
  }

  // Metal operations
  Future<void> addMetal(Metal metal) async {
    await _dataService.saveMetal(metal);
    _metals = await _dataService.getMetals();
    notifyListeners();
  }

  Future<void> updateMetal(Metal metal) async {
    await _dataService.saveMetal(metal);
    _metals = await _dataService.getMetals();
    notifyListeners();
  }

  Future<void> deleteMetal(String id) async {
    await _dataService.deleteMetal(id);
    _metals = await _dataService.getMetals();
    notifyListeners();
  }

  // Purity operations
  Future<void> addPurity(Purity purity) async {
    await _dataService.savePurity(purity);
    _purities = await _dataService.getPurities();
    notifyListeners();
  }

  Future<void> updatePurity(Purity purity) async {
    await _dataService.savePurity(purity);
    _purities = await _dataService.getPurities();
    notifyListeners();
  }

  Future<void> deletePurity(String id) async {
    await _dataService.deletePurity(id);
    _purities = await _dataService.getPurities();
    notifyListeners();
  }

  // Item Inward operations
  Future<void> addItemInward(ItemInward itemInward) async {
    await _dataService.saveItemInward(itemInward);
    _itemInwards = await _dataService.getItemInwards();
    notifyListeners();
  }

  Future<void> updateItemInward(ItemInward itemInward) async {
    await _dataService.saveItemInward(itemInward);
    _itemInwards = await _dataService.getItemInwards();
    notifyListeners();
  }

  Future<void> deleteItemInward(String id) async {
    await _dataService.deleteItemInward(id);
    _itemInwards = await _dataService.getItemInwards();
    notifyListeners();
  }

  // Sales operations
  Future<void> addSale(Sales sale) async {
    await _dataService.saveSale(sale);
    _sales = await _dataService.getSales();
    notifyListeners();
  }

  Future<void> updateSale(Sales sale) async {
    await _dataService.saveSale(sale);
    _sales = await _dataService.getSales();
    notifyListeners();
  }

  Future<void> deleteSale(String id) async {
    await _dataService.deleteSale(id);
    _sales = await _dataService.getSales();
    notifyListeners();
  }

  // Helper methods
  Customer? getCustomerById(String id) {
    try {
      return _customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  Item? getItemById(String id) {
    try {
      return _items.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  Worker? getWorkerById(String id) {
    try {
      return _workers.firstWhere((worker) => worker.id == id);
    } catch (e) {
      return null;
    }
  }

  Metal? getMetalById(String id) {
    try {
      return _metals.firstWhere((metal) => metal.id == id);
    } catch (e) {
      return null;
    }
  }

  Purity? getPurityById(String id) {
    try {
      return _purities.firstWhere((purity) => purity.id == id);
    } catch (e) {
      return null;
    }
  }

  // Statistics
  int get totalCustomers => _customers.length;
  int get activeCustomers => _customers.where((c) => c.status == 'Active').length;
  int get totalItems => _items.length;
  int get totalWorkers => _workers.length;
  double get totalSalesAmount => _sales.fold(0.0, (sum, sale) => sum + (sale.amount ?? 0.0));
}
