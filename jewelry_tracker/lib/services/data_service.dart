import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/customer.dart';
import '../models/item.dart';
import '../models/worker.dart';
import '../models/metal.dart';
import '../models/purity.dart';
import '../models/item_inward.dart';
import '../models/sales.dart';

class DataService {
  static final DataService _instance = DataService._internal();
  factory DataService() => _instance;
  DataService._internal();

  final Uuid _uuid = const Uuid();
  late SharedPreferences _prefs;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _initializeSampleData();
  }

  // Generate unique IDs
  String generateId(String prefix) {
    return '${prefix.toUpperCase()}${DateTime.now().millisecondsSinceEpoch}';
  }

  // Customer operations
  Future<List<Customer>> getCustomers() async {
    final String? customersJson = _prefs.getString('customers');
    if (customersJson == null) return [];
    
    final List<dynamic> customersList = json.decode(customersJson);
    return customersList.map((json) => Customer.fromJson(json)).toList();
  }

  Future<void> saveCustomer(Customer customer) async {
    final customers = await getCustomers();
    final index = customers.indexWhere((c) => c.id == customer.id);
    
    if (index >= 0) {
      customers[index] = customer.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      customers.add(customer);
    }
    
    await _prefs.setString('customers', json.encode(customers.map((c) => c.toJson()).toList()));
  }

  Future<void> deleteCustomer(String id) async {
    final customers = await getCustomers();
    customers.removeWhere((c) => c.id == id);
    await _prefs.setString('customers', json.encode(customers.map((c) => c.toJson()).toList()));
  }

  // Item operations
  Future<List<Item>> getItems() async {
    final String? itemsJson = _prefs.getString('items');
    if (itemsJson == null) return [];
    
    final List<dynamic> itemsList = json.decode(itemsJson);
    return itemsList.map((json) => Item.fromJson(json)).toList();
  }

  Future<void> saveItem(Item item) async {
    final items = await getItems();
    final index = items.indexWhere((i) => i.id == item.id);
    
    if (index >= 0) {
      items[index] = item.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      items.add(item);
    }
    
    await _prefs.setString('items', json.encode(items.map((i) => i.toJson()).toList()));
  }

  Future<void> deleteItem(String id) async {
    final items = await getItems();
    items.removeWhere((i) => i.id == id);
    await _prefs.setString('items', json.encode(items.map((i) => i.toJson()).toList()));
  }

  // Worker operations
  Future<List<Worker>> getWorkers() async {
    final String? workersJson = _prefs.getString('workers');
    if (workersJson == null) return [];
    
    final List<dynamic> workersList = json.decode(workersJson);
    return workersList.map((json) => Worker.fromJson(json)).toList();
  }

  Future<void> saveWorker(Worker worker) async {
    final workers = await getWorkers();
    final index = workers.indexWhere((w) => w.id == worker.id);
    
    if (index >= 0) {
      workers[index] = worker.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      workers.add(worker);
    }
    
    await _prefs.setString('workers', json.encode(workers.map((w) => w.toJson()).toList()));
  }

  Future<void> deleteWorker(String id) async {
    final workers = await getWorkers();
    workers.removeWhere((w) => w.id == id);
    await _prefs.setString('workers', json.encode(workers.map((w) => w.toJson()).toList()));
  }

  // Metal operations
  Future<List<Metal>> getMetals() async {
    final String? metalsJson = _prefs.getString('metals');
    if (metalsJson == null) return [];
    
    final List<dynamic> metalsList = json.decode(metalsJson);
    return metalsList.map((json) => Metal.fromJson(json)).toList();
  }

  Future<void> saveMetal(Metal metal) async {
    final metals = await getMetals();
    final index = metals.indexWhere((m) => m.id == metal.id);
    
    if (index >= 0) {
      metals[index] = metal.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      metals.add(metal);
    }
    
    await _prefs.setString('metals', json.encode(metals.map((m) => m.toJson()).toList()));
  }

  Future<void> deleteMetal(String id) async {
    final metals = await getMetals();
    metals.removeWhere((m) => m.id == id);
    await _prefs.setString('metals', json.encode(metals.map((m) => m.toJson()).toList()));
  }

  // Purity operations
  Future<List<Purity>> getPurities() async {
    final String? puritiesJson = _prefs.getString('purities');
    if (puritiesJson == null) return [];
    
    final List<dynamic> puritiesList = json.decode(puritiesJson);
    return puritiesList.map((json) => Purity.fromJson(json)).toList();
  }

  Future<void> savePurity(Purity purity) async {
    final purities = await getPurities();
    final index = purities.indexWhere((p) => p.id == purity.id);
    
    if (index >= 0) {
      purities[index] = purity.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      purities.add(purity);
    }
    
    await _prefs.setString('purities', json.encode(purities.map((p) => p.toJson()).toList()));
  }

  Future<void> deletePurity(String id) async {
    final purities = await getPurities();
    purities.removeWhere((p) => p.id == id);
    await _prefs.setString('purities', json.encode(purities.map((p) => p.toJson()).toList()));
  }

  // Item Inward operations
  Future<List<ItemInward>> getItemInwards() async {
    final String? itemInwardsJson = _prefs.getString('itemInwards');
    if (itemInwardsJson == null) return [];
    
    final List<dynamic> itemInwardsList = json.decode(itemInwardsJson);
    return itemInwardsList.map((json) => ItemInward.fromJson(json)).toList();
  }

  Future<void> saveItemInward(ItemInward itemInward) async {
    final itemInwards = await getItemInwards();
    final index = itemInwards.indexWhere((i) => i.id == itemInward.id);
    
    if (index >= 0) {
      itemInwards[index] = itemInward.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      itemInwards.add(itemInward);
    }
    
    await _prefs.setString('itemInwards', json.encode(itemInwards.map((i) => i.toJson()).toList()));
  }

  Future<void> deleteItemInward(String id) async {
    final itemInwards = await getItemInwards();
    itemInwards.removeWhere((i) => i.id == id);
    await _prefs.setString('itemInwards', json.encode(itemInwards.map((i) => i.toJson()).toList()));
  }

  // Sales operations
  Future<List<Sales>> getSales() async {
    final String? salesJson = _prefs.getString('sales');
    if (salesJson == null) return [];
    
    final List<dynamic> salesList = json.decode(salesJson);
    return salesList.map((json) => Sales.fromJson(json)).toList();
  }

  Future<void> saveSale(Sales sale) async {
    final sales = await getSales();
    final index = sales.indexWhere((s) => s.id == sale.id);
    
    if (index >= 0) {
      sales[index] = sale.copyWith(
        updatedAt: DateTime.now(),
        updatedBy: '<EMAIL>',
      );
    } else {
      sales.add(sale);
    }
    
    await _prefs.setString('sales', json.encode(sales.map((s) => s.toJson()).toList()));
  }

  Future<void> deleteSale(String id) async {
    final sales = await getSales();
    sales.removeWhere((s) => s.id == id);
    await _prefs.setString('sales', json.encode(sales.map((s) => s.toJson()).toList()));
  }

  // Authentication
  Future<bool> login(String username, String password) async {
    if (username == '<EMAIL>' && password == 'Admin@1234') {
      await _prefs.setBool('isLoggedIn', true);
      return true;
    }
    return false;
  }

  Future<void> logout() async {
    await _prefs.setBool('isLoggedIn', false);
  }

  Future<bool> isLoggedIn() async {
    return _prefs.getBool('isLoggedIn') ?? false;
  }

  // Initialize sample data
  Future<void> _initializeSampleData() async {
    final customers = await getCustomers();
    if (customers.isEmpty) {
      // Add sample customers
      await saveCustomer(Customer(
        id: 'CUSTOMER001',
        name: 'Rajesh Kumar',
        balance: 5000.0,
        mobile: '9876543210',
        email: '<EMAIL>',
        address: '123 Main Street, Mumbai',
        remarks: 'Regular customer',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      await saveCustomer(Customer(
        id: 'CUSTOMER002',
        name: 'Priya Sharma',
        balance: 3000.0,
        mobile: '9876543211',
        email: '<EMAIL>',
        address: '456 Park Avenue, Delhi',
        remarks: 'VIP customer',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      // Add sample items
      await saveItem(Item(
        id: 'ITEM001',
        number: 'ITM-001',
        description: 'Gold Ring 22K',
        remarks: 'Wedding collection',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      await saveItem(Item(
        id: 'ITEM002',
        number: 'ITM-002',
        description: 'Silver Necklace',
        remarks: 'Traditional design',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      // Add sample workers
      await saveWorker(Worker(
        id: 'WORKER001',
        name: 'Suresh Goldsmith',
        balance: 2000.0,
        remarks: 'Expert in ring making',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      // Add sample metals
      await saveMetal(Metal(
        id: 'METAL001',
        description: 'Gold 22K',
        remarks: 'High purity gold',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      await saveMetal(Metal(
        id: 'METAL002',
        description: 'Silver 925',
        remarks: 'Sterling silver',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      // Add sample purities
      await savePurity(Purity(
        id: 'PURITY001',
        name: '22K Gold',
        metalType: 'Gold',
        description: '22 Karat Gold',
        remarks: 'Standard purity',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));

      await savePurity(Purity(
        id: 'PURITY002',
        name: '925 Silver',
        metalType: 'Silver',
        description: 'Sterling Silver',
        remarks: 'Standard silver purity',
        status: 'Active',
        createdAt: DateTime.now(),
        createdBy: '<EMAIL>',
      ));
    }
  }
}
