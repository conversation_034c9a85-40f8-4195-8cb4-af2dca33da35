import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:intl/intl.dart';
import '../../providers/data_provider.dart';
import '../../models/customer.dart';
import '../../services/data_service.dart';
import '../forms/customer_form.dart';

class CustomersTab extends StatefulWidget {
  const CustomersTab({super.key});

  @override
  State<CustomersTab> createState() => _CustomersTabState();
}

class _CustomersTabState extends State<CustomersTab> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DataProvider>(
      builder: (context, dataProvider, child) {
        final filteredCustomers = dataProvider.customers.where((customer) {
          return customer.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                 customer.id.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                 (customer.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
        }).toList();

        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Text(
                    'Customer Master',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: () => _showCustomerForm(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Customer'),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Search Bar
              SizedBox(
                width: 300,
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search customers...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
              const SizedBox(height: 16),

              // Data Table
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: DataTable2(
                      columnSpacing: 12,
                      horizontalMargin: 12,
                      minWidth: 800,
                      columns: const [
                        DataColumn2(label: Text('Customer ID'), size: ColumnSize.S),
                        DataColumn2(label: Text('Name'), size: ColumnSize.L),
                        DataColumn2(label: Text('Balance'), size: ColumnSize.S),
                        DataColumn2(label: Text('Mobile'), size: ColumnSize.M),
                        DataColumn2(label: Text('Email'), size: ColumnSize.L),
                        DataColumn2(label: Text('Status'), size: ColumnSize.S),
                        DataColumn2(label: Text('Actions'), size: ColumnSize.S),
                      ],
                      rows: filteredCustomers.map((customer) {
                        return DataRow2(
                          cells: [
                            DataCell(Text(customer.id)),
                            DataCell(Text(customer.name)),
                            DataCell(Text('₹${NumberFormat('#,##,###').format(customer.balance)}')),
                            DataCell(Text(customer.mobile ?? '-')),
                            DataCell(Text(customer.email ?? '-')),
                            DataCell(
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: customer.status == 'Active' ? Colors.green.shade100 : Colors.red.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  customer.status,
                                  style: TextStyle(
                                    color: customer.status == 'Active' ? Colors.green.shade800 : Colors.red.shade800,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            DataCell(
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit, size: 18),
                                    onPressed: () => _showCustomerForm(context, customer: customer),
                                    tooltip: 'Edit',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                                    onPressed: () => _deleteCustomer(context, customer),
                                    tooltip: 'Delete',
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showCustomerForm(BuildContext context, {Customer? customer}) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          padding: const EdgeInsets.all(24),
          child: CustomerForm(
            customer: customer,
            onSave: (customer) {
              final dataProvider = Provider.of<DataProvider>(context, listen: false);
              if (customer.id.isEmpty) {
                // Add new customer
                final newCustomer = customer.copyWith(
                  id: DataService().generateId('CUSTOMER'),
                  createdAt: DateTime.now(),
                  createdBy: '<EMAIL>',
                );
                dataProvider.addCustomer(newCustomer);
              } else {
                // Update existing customer
                dataProvider.updateCustomer(customer);
              }
              Navigator.of(context).pop();
            },
          ),
        ),
      ),
    );
  }

  void _deleteCustomer(BuildContext context, Customer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Customer'),
        content: Text('Are you sure you want to delete ${customer.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Provider.of<DataProvider>(context, listen: false).deleteCustomer(customer.id);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
