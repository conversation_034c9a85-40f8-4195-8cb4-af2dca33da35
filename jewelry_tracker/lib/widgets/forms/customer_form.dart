import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import '../../models/customer.dart';

class CustomerForm extends StatefulWidget {
  final Customer? customer;
  final Function(Customer) onSave;

  const CustomerForm({
    super.key,
    this.customer,
    required this.onSave,
  });

  @override
  State<CustomerForm> createState() => _CustomerFormState();
}

class _CustomerFormState extends State<CustomerForm> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Text(
          widget.customer == null ? 'Add New Customer' : 'Edit Customer',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),

        // Form
        Flexible(
          child: FormBuilder(
            key: _formKey,
            initialValue: {
              'id': widget.customer?.id ?? '',
              'name': widget.customer?.name ?? '',
              'balance': widget.customer?.balance.toString() ?? '0',
              'mobile': widget.customer?.mobile ?? '',
              'email': widget.customer?.email ?? '',
              'address': widget.customer?.address ?? '',
              'remarks': widget.customer?.remarks ?? '',
              'status': widget.customer?.status ?? 'Active',
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Customer ID and Name
                  Row(
                    children: [
                      Expanded(
                        child: FormBuilderTextField(
                          name: 'id',
                          decoration: const InputDecoration(
                            labelText: 'Customer ID',
                            hintText: 'Auto-generated',
                            border: OutlineInputBorder(),
                          ),
                          enabled: widget.customer != null,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: FormBuilderTextField(
                          name: 'name',
                          decoration: const InputDecoration(
                            labelText: 'Customer Name *',
                            border: OutlineInputBorder(),
                          ),
                          validator: FormBuilderValidators.required(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Balance and Mobile
                  Row(
                    children: [
                      Expanded(
                        child: FormBuilderTextField(
                          name: 'balance',
                          decoration: const InputDecoration(
                            labelText: 'Balance',
                            prefixText: '₹ ',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.numeric(),
                          ]),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: FormBuilderTextField(
                          name: 'mobile',
                          decoration: const InputDecoration(
                            labelText: 'Mobile Number',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.phone,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.phoneNumber(),
                          ]),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Email
                  FormBuilderTextField(
                    name: 'email',
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: FormBuilderValidators.compose([
                      FormBuilderValidators.email(),
                    ]),
                  ),
                  const SizedBox(height: 16),

                  // Address
                  FormBuilderTextField(
                    name: 'address',
                    decoration: const InputDecoration(
                      labelText: 'Address',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 16),

                  // Status
                  FormBuilderDropdown<String>(
                    name: 'status',
                    decoration: const InputDecoration(
                      labelText: 'Status',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'Active', child: Text('Active')),
                      DropdownMenuItem(value: 'Inactive', child: Text('Inactive')),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Remarks
                  FormBuilderTextField(
                    name: 'remarks',
                    decoration: const InputDecoration(
                      labelText: 'Remarks',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),

        // Actions
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _saveCustomer,
              child: Text(widget.customer == null ? 'Add Customer' : 'Update Customer'),
            ),
          ],
        ),
      ],
    );
  }

  void _saveCustomer() {
    if (_formKey.currentState!.saveAndValidate()) {
      final formData = _formKey.currentState!.value;
      
      final customer = Customer(
        id: formData['id'] ?? '',
        name: formData['name'],
        balance: double.tryParse(formData['balance']) ?? 0.0,
        mobile: formData['mobile'].isEmpty ? null : formData['mobile'],
        email: formData['email'].isEmpty ? null : formData['email'],
        address: formData['address'].isEmpty ? null : formData['address'],
        remarks: formData['remarks'].isEmpty ? null : formData['remarks'],
        status: formData['status'],
        createdAt: widget.customer?.createdAt ?? DateTime.now(),
        createdBy: widget.customer?.createdBy ?? '<EMAIL>',
        updatedAt: widget.customer != null ? DateTime.now() : null,
        updatedBy: widget.customer != null ? '<EMAIL>' : null,
      );

      widget.onSave(customer);
    }
  }
}
