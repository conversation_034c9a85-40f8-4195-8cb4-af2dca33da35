# Jewelry Tracker - Client Setup Instructions

## 🎯 Quick Start (For Windows 10 Pro x64)

### Method 1: Using the Improved Batch File
1. **Double-click** `start-jewelry-tracker.bat`
2. **Wait** for the installation process (may take 2-3 minutes first time)
3. **Look for** the success message with server URL
4. **Open browser** and go to `http://localhost:3000`
5. **Login** with: `<EMAIL>` / `Admin@1234`

### Method 2: Using PowerShell (Recommended)
1. **Right-click** on `start-jewelry-tracker.ps1`
2. **Select** "Run with PowerShell"
3. **If prompted**, type `Y` to allow execution
4. **Follow** the on-screen instructions

### Method 3: Manual Command Prompt
1. **Press** `Win + R`, type `cmd`, press `Ctrl + Shift + Enter` (Run as Admin)
2. **Navigate** to project folder:
   ```cmd
   cd C:\path\to\jewelry-tracker
   ```
3. **Install dependencies**:
   ```cmd
   npm install
   ```
4. **Start server**:
   ```cmd
   npm run dev
   ```

## 🔧 If Batch File Closes Immediately

This usually happens due to one of these reasons:

### Reason 1: Node.js Not Installed
**Solution:**
1. Download Node.js from: https://nodejs.org/
2. Install the LTS version (recommended)
3. Restart your computer
4. Try the batch file again

### Reason 2: Permission Issues
**Solution:**
1. Right-click on `start-jewelry-tracker.bat`
2. Select "Run as administrator"
3. Click "Yes" when prompted

### Reason 3: Path Issues
**Solution:**
1. Make sure you're running the batch file from the correct project folder
2. The folder should contain `package.json` file

## 🚨 Troubleshooting

### Run the Troubleshoot Tool
1. **Double-click** `troubleshoot.bat`
2. **Review** all the checks
3. **Fix** any issues marked with ❌

### Common Issues and Solutions

**Issue: "Database connection failed"**
- ✅ Start WAMP server
- ✅ Ensure MySQL service is green in WAMP tray
- ✅ Check phpMyAdmin: `http://localhost/phpmyadmin`

**Issue: "Port 3000 already in use"**
- ✅ Close other applications using port 3000
- ✅ Or wait for the server to find an alternative port

**Issue: "npm command not found"**
- ✅ Install Node.js from https://nodejs.org/
- ✅ Restart Command Prompt
- ✅ Verify: `node --version`

## 📱 How to Access the Application

### Step 1: Start the Server
- Use any of the methods above
- Wait for the message: "Server running on http://localhost:3000"

### Step 2: Open in Browser
- Open any web browser (Chrome, Firefox, Edge)
- Go to: `http://localhost:3000`
- You should see the login page

### Step 3: Login
- **Username**: `<EMAIL>`
- **Password**: `Admin@1234`
- Click "Sign In"

### Step 4: Use the Application
- **Dashboard**: View statistics and quick actions
- **Masters**: Manage customers, items, workers, metals, purities
- **Transactions**: Record item inwards, sales, etc.
- **History**: View reports and transaction history

## 🌐 Network Access (Optional)

To allow other computers on your network to access:

### Step 1: Find Your IP Address
1. Press `Win + R`, type `cmd`, press Enter
2. Type: `ipconfig`
3. Note your IPv4 Address (e.g., *************)

### Step 2: Configure Firewall
1. Open Windows Firewall
2. Allow Node.js through firewall
3. Allow port 3000

### Step 3: Access from Other Computers
- Use: `http://YOUR_IP_ADDRESS:3000`
- Example: `http://*************:3000`

## 💾 Data Storage

- **Database**: All data is stored in MySQL database `jewelry_tracker`
- **Backup**: Use phpMyAdmin to export database regularly
- **Location**: WAMP MySQL data folder

## 🔄 Starting/Stopping the Application

### To Start:
- Run `start-jewelry-tracker.bat`
- Or use PowerShell script
- Or manual command prompt method

### To Stop:
- Press `Ctrl + C` in the command window
- Or close the command window
- The database remains intact

### To Restart:
- Stop the application
- Start it again using any method
- All data will be preserved

## 📞 Support Checklist

Before asking for help, please check:

- ✅ WAMP server is running (green icons)
- ✅ Node.js is installed (`node --version` works)
- ✅ Database exists in phpMyAdmin
- ✅ No other application is using port 3000
- ✅ Running as Administrator if needed
- ✅ Internet connection for initial npm install

## 🎉 Success Indicators

You know everything is working when:

1. **Command window shows**:
   ```
   ✅ Database connected successfully
   🚀 Server running on http://localhost:3000
   ```

2. **Browser shows**: Login page at `http://localhost:3000`

3. **Can login**: With <EMAIL> / Admin@1234

4. **Dashboard loads**: Shows statistics and sample data

5. **Can add data**: Create customers, items, etc.

6. **Data persists**: After restarting the application

---

## 📋 Quick Reference

**Start Application**: Double-click `start-jewelry-tracker.bat`
**Access URL**: `http://localhost:3000`
**Login**: `<EMAIL>` / `Admin@1234`
**Stop**: Press `Ctrl + C` in command window
**Database**: `http://localhost/phpmyadmin`
**Troubleshoot**: Run `troubleshoot.bat`

**🚀 Your Jewelry Tracker is ready to use!**
