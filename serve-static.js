import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3000;

// Serve static files from dist/static directory
app.use(express.static(path.join(__dirname, 'dist/static')));

// Handle client-side routing - serve index.html for all routes
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/static/index.static.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 Static Jewelry Tracker is running at http://localhost:${PORT}`);
  console.log('📱 Login credentials: <EMAIL> / Admin@1234');
  console.log('💾 Data is stored in browser localStorage');
});
