#!/bin/bash

echo "========================================"
echo "    Jewelry Tracker Application"
echo "========================================"
echo

echo "Checking if Node.js is installed..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js is installed."
echo

echo "Installing dependencies..."
npm install

echo
echo "Starting the application..."
echo
echo "The application will be available at: http://localhost:3000"
echo "Login credentials:"
echo "  Username: <EMAIL>"
echo "  Password: Admin@1234"
echo
echo "If MySQL is running (XAMPP/WAMP), it will use the database."
echo "Otherwise, it will use local storage."
echo

npm run dev
