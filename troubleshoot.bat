@echo off
title Jewelry Tracker Troubleshoot
color 0E
echo ========================================
echo   Jewelry Tracker Troubleshoot Tool
echo ========================================
echo.

echo Running system diagnostics...
echo.

echo [1] Checking Node.js installation:
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js is NOT installed
    echo Download from: https://nodejs.org/
) else (
    echo ✅ Node.js is installed
)
echo.

echo [2] Checking npm installation:
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm is NOT available
) else (
    echo ✅ npm is available
)
echo.

echo [3] Checking if port 3000 is available:
netstat -an | findstr :3000
if %errorlevel% equ 0 (
    echo ⚠️  Port 3000 is already in use
    echo You may need to close other applications or change the port
) else (
    echo ✅ Port 3000 is available
)
echo.

echo [4] Checking project files:
if exist package.json (
    echo ✅ package.json found
) else (
    echo ❌ package.json NOT found - make sure you're in the correct directory
)

if exist server\index.ts (
    echo ✅ server files found
) else (
    echo ❌ server files NOT found - make sure you're in the correct directory
)
echo.

echo [5] Checking WAMP services:
echo Please manually verify in WAMP tray icon:
echo - Apache service should be GREEN
echo - MySQL service should be GREEN
echo.

echo [6] Testing database connection:
echo Open browser and go to: http://localhost/phpmyadmin
echo Login with username: root, password: jewelry123
echo Check if 'jewelry_tracker' database exists
echo.

echo ========================================
echo Troubleshooting complete!
echo.
echo If all checks pass, try running:
echo   start-jewelry-tracker.bat
echo.
echo If issues persist:
echo 1. Run Command Prompt as Administrator
echo 2. Navigate to project folder
echo 3. Run: npm install
echo 4. Run: npm run dev
echo ========================================
echo.
pause
