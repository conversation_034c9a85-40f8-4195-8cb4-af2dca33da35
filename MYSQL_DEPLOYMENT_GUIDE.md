# Jewelry Tracker - MySQL Database Deployment Guide

## 🎯 **Overview**

This guide will help you deploy the **full MySQL database version** of Jewelry Tracker on your WAMP server, which stores all data in MySQL database instead of browser localStorage.

## ✅ **Prerequisites Checklist**

- ✅ WAMP Server installed and running
- ✅ MySQL service running (green in WAMP tray)
- ✅ Database `jewelry_tracker` created
- ✅ All tables created using `database-setup.sql`
- ✅ MySQL username: `root`, password: `jewelry123`
- ✅ Node.js installed
- ✅ phpMyAdmin accessible at `http://localhost/phpmyadmin`

## 🔧 **Current Configuration**

Your database configuration is already set correctly:
```javascript
{
  host: 'localhost',
  user: 'root',
  password: 'jewelry123',
  database: 'jewelry_tracker',
  port: 3306
}
```

## 🚀 **Deployment Steps**

### Step 1: Verify WAMP Setup
1. **Start WAMP Server**
2. **Check MySQL is green** in WAMP tray icon
3. **Test phpMyAdmin**: Go to `http://localhost/phpmyadmin`
4. **Login**: Username `root`, Password `jewelry123`
5. **Verify database**: Check `jewelry_tracker` database exists with all tables

### Step 2: Start the Application Server
1. **Open Command Prompt as Administrator**
2. **Navigate to project folder**:
   ```cmd
   cd C:\path\to\your\jewelry-tracker-project
   ```
3. **Install dependencies** (first time only):
   ```cmd
   npm install
   ```
4. **Start the server**:
   ```cmd
   npm run dev
   ```

### Step 3: Access the Application
1. **Wait for success message**:
   ```
   ✅ Database connected successfully
   ✅ Database initialized successfully
   🚀 Server running on http://localhost:3000
   ```
2. **Open browser**: `http://localhost:3000`
3. **Login**: `<EMAIL>` / `Admin@1234`

## 📊 **Data Storage Verification**

### How to Verify MySQL Storage:
1. **Add a customer** in the application
2. **Check phpMyAdmin**: 
   - Go to `http://localhost/phpmyadmin`
   - Select `jewelry_tracker` database
   - Click on `customers` table
   - Click "Browse" - you should see your data

### Database Tables:
- `customers` - Customer information
- `items` - Item catalog
- `workers` - Worker details
- `metals` - Metal types
- `purities` - Purity levels
- `item_inwards` - Item inward transactions
- `item_outwards` - Item outward transactions
- `metal_inwards` - Metal inward transactions
- `metal_outwards` - Metal outward transactions
- `sales` - Sales records

## 🔄 **Key Differences from Static Version**

| Feature | Static Version | MySQL Version |
|---------|---------------|---------------|
| **Data Storage** | Browser localStorage | MySQL Database |
| **Data Persistence** | Browser only | Permanent in database |
| **Multi-user** | Single browser | Multiple users possible |
| **Backup** | Export JSON | MySQL backup |
| **Server Required** | No | Yes (Node.js) |
| **Database Required** | No | Yes (MySQL) |

## 🛠 **Troubleshooting**

### Issue: "Database connection failed"
**Solutions:**
1. Check WAMP MySQL service is running (green)
2. Verify database credentials in phpMyAdmin
3. Ensure `jewelry_tracker` database exists
4. Check if port 3306 is available

### Issue: "Tables not found"
**Solutions:**
1. Run the `database-setup.sql` file in phpMyAdmin
2. Or let the application create tables automatically

### Issue: "Port 3000 already in use"
**Solutions:**
1. Close other applications using port 3000
2. Or change port in `server/index.ts`

### Issue: Server won't start
**Solutions:**
1. Run Command Prompt as Administrator
2. Check Node.js is installed: `node --version`
3. Install dependencies: `npm install`
4. Check for error messages in console

## 📱 **Features with MySQL Database**

### ✅ **All Features Available:**
- **Customer Management** - Stored in `customers` table
- **Item Management** - Stored in `items` table
- **Worker Management** - Stored in `workers` table
- **Metal & Purity Management** - Stored in respective tables
- **Transaction Management** - All transaction types stored
- **Sales Management** - Stored in `sales` table
- **Add Sale from Transactions** - Creates database records
- **Reports & History** - Real-time from database
- **Data Export** - From database tables

### ✅ **Additional Benefits:**
- **Data Backup** - Use MySQL backup tools
- **Data Recovery** - Restore from MySQL backups
- **Multi-user Access** - Multiple people can use simultaneously
- **Data Integrity** - Foreign key constraints
- **Performance** - Optimized database queries
- **Scalability** - Can handle large amounts of data

## 🔐 **Security Notes**

- Database password is set to `jewelry123`
- Application runs on localhost only
- No external network access by default
- All data stored locally on client machine

## 📋 **Daily Usage**

### To Start Application:
1. **Start WAMP** (if not running)
2. **Run**: `start-jewelry-tracker.bat` or manual command prompt
3. **Access**: `http://localhost:3000`

### To Stop Application:
1. **Press Ctrl+C** in command window
2. **Or close** command window
3. **Data remains** in MySQL database

### To Backup Data:
1. **Open phpMyAdmin**
2. **Select** `jewelry_tracker` database
3. **Click** "Export"
4. **Download** SQL file

## ✅ **Success Verification**

Your MySQL deployment is successful when:
- ✅ WAMP MySQL service is green
- ✅ Server starts without database errors
- ✅ Can login at `http://localhost:3000`
- ✅ Can add customers/items and they appear in phpMyAdmin
- ✅ Data persists after server restart
- ✅ All transaction types work
- ✅ "Add Sale" feature creates database records

## 🎯 **Quick Start Commands**

```cmd
# Start WAMP Server first, then:
cd C:\path\to\jewelry-tracker
npm install
npm run dev
# Open browser: http://localhost:3000
```

**🎉 Your MySQL-powered Jewelry Tracker is ready!**

---

## 📞 **Need Help?**

If you encounter issues:
1. Check WAMP services are running
2. Verify database exists in phpMyAdmin
3. Run troubleshoot.bat for diagnostics
4. Check command window for error messages
