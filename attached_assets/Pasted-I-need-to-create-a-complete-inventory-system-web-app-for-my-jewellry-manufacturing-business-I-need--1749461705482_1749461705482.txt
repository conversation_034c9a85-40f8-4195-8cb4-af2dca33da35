I need to create a complete inventory system web app for my jewellry manufacturing business. I need the complete features of below.
I need to create a web app with five main menu like Home, Masters, Transactions, History and logout, you need to create login page to access this web page, as of now you need to validate the username as "<EMAIL>" and the password is "Admin@1234", If  the user entered the correct username and password then you need to navigate into the home page page, The home page is showing the all the dashboards like recent inward items, and recent outward items, recent metal inward, recent metal outwards, sales, and other charts and other data, but you need to fully cover the dashboard with useful widgets.  And also I need the Add items, add worker, add customer options required in the dashboard itself for ease of access. And in this Masters main menu I need the sub menu list as Customer master, Item master, Workers master, and Metal master. In this customer master I need add new customer, edit existing customers and delete the existing customer and export options also, I need these features all these masters. After that in the transaction sub menu, I need the Item inward, Item outward, Metal inward, Metal outward, and Sales. In the history page, I need all the history options for example, All the transactions are showing as report format, example, I can able to search Item inward id, outward id, sales and metal inward or outward id. I can set start date and end date and search in-between dates transactions. And others. I have mentioned all the required fields below for all the masters and transactions. And all the fields like created date, created by, status(active or inactive), updated by, updated on and fields like these are common for all masters and transactions.

Master:(Add new, Edit, Delete, Export, Search, Select, Sort by)
	- Customer(AutoGenerated  CustomerID, Customer Name(textfield, Customer Balance(textfield, Mobile no(textfield, mail(textfield, Address(textfield, Remarks(textfield)
	- Item (AutoGenerated ItemID, Item number(textfield, Item description(textfield, Remarks(textfield)
	- Workers (AutoGenerated WorkerID. Worker Name(textfield, Balance(textfield, Remarks(textfield)
	- Metal (AutoGenerated MetalID, Metal description(textfield, Remarks(textfield)
Transaction:(Add new, Edit, Delete, Export, Search, Select, Sort by)
	- Item inward(AutoGenerated item inwardID, Outward Date(Date picker), Customer(fetch from customer), Item(fetch from item), Melting(textfield), Gross Weight(textfield), ST(textfield), EN(textfield), THD(textfield), Piece count(textfield), Net Weight(textfield), Comments(textfield))
	-  Item outward(AutoGenerated item OutwardID, Outward Date(Date picker), Customer(fetch from customer), Item(fetch from item), Melting(textfield), Gross Weight(textfield), ST(textfield), EN(textfield), THD(textfield), Piece count(textfield), Net Weight(textfield), Comments(textfield))
	- Metal inward(AutoGenerated metal inwardID, Outward Date(Date picker), Customer(fetch from customer), Item(fetch from item), Weight(textfield), Purity(Textfield), Rate(Textfield), Piece count(textfield), Net Weight(textfield), Comments(textfield))
Sales: (Add new, Edit, Delete, Export, Search, Select, Sort by)
	- Sales(Auto Generated SaleID, Sale Date(Date picker), Customer(fetch from customer), Item(fetch from item), Melting(textfield), Gross Weight(textfield), ST(textfield), EN(textfield), THD(textfield), Piece count(textfield), Net Weight(textfield), Comments(textfield))

History:
	- Item inward Report
	- Item outward Report
	- Metal inward Report
	- Metal outward Report
	- Sales Report

I need complete working real world inventory system, as of now all the masters and transactions  and sales details are stored in the json format in the same project, Also I need the one more master called Purity(Auto generated PurityID, Purity Name, Metal Type, Description, Remarks.)

The keyboard which is shown based on the below fields types:

Customer Name(Text), Customer Balance(Double), Mobile no(int(13)), mail(mail format), Address(text), Item number(text), Item description(text), Worker Name(text), Balance(double), Metal description(text), Outward Date(Date picker),