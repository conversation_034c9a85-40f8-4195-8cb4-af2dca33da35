@echo off
echo Creating desktop shortcut for Jewelry Tracker...

set SCRIPT="%TEMP%\%RANDOM%-%RANDOM%-%RANDOM%-%RANDOM%.vbs"

echo Set oWS = WScript.CreateObject("WScript.Shell") >> %SCRIPT%
echo sLinkFile = "%USERPROFILE%\Desktop\Jewelry Tracker.lnk" >> %SCRIPT%
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> %SCRIPT%
echo oLink.TargetPath = "%~dp0start-jewelry-tracker.bat" >> %SCRIPT%
echo oLink.WorkingDirectory = "%~dp0" >> %SCRIPT%
echo oLink.Description = "Jewelry Tracker Inventory Management System" >> %SCRIPT%
echo oLink.IconLocation = "%SystemRoot%\System32\shell32.dll,43" >> %SCRIPT%
echo oLink.Save >> %SCRIPT%

cscript /nologo %SCRIPT%
del %SCRIPT%

echo ✅ Desktop shortcut created successfully!
echo You can now double-click "Jewelry Tracker" on your desktop to start the application.
echo.
pause
