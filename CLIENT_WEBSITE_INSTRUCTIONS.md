# Jewelry Tracker Website - Client Instructions

## 🎯 **What You Received**

You received a **complete website** in the `dist/static/` folder that works without any complex setup.

## 📁 **The Website Folder**

**Folder Name**: `dist/static/`
**Contains**: Complete jewelry tracking website
**Size**: Small (just a few MB)
**Requirements**: Any web browser

## 🚀 **How to Host on Your WAMP Server**

### Step 1: Copy Website Files
1. **Find** the `dist/static/` folder
2. **Copy** the entire folder
3. **Go to** your WAMP installation folder:
   ```
   C:\wamp64\www\
   ```
4. **Paste** the folder there
5. **Rename** it to `jewelry-tracker` (optional)

### Step 2: Start WAMP
1. **Start WAMP** server
2. **Make sure Apache** is running (green icon)
3. **MySQL is NOT required** for this website

### Step 3: Access Website
1. **Open any web browser** (Chrome, Firefox, Edge)
2. **Type**: `http://localhost/jewelry-tracker/`
3. **Press Enter**

### Step 4: Login
- **Username**: `<EMAIL>`
- **Password**: `Admin@1234`
- **Click**: "Sign In"

## 🎉 **That's It! Your Website is Running**

## 📱 **What You Can Do**

### ✅ **Customer Management**
- Add new customers
- Edit customer details
- View customer balances
- Delete customers

### ✅ **Item Management**
- Add jewelry items
- Edit item descriptions
- Manage item catalog
- Track item status

### ✅ **Worker Management**
- Add worker details
- Track worker balances
- Manage worker information

### ✅ **Metal & Purity Management**
- Manage metal types (Gold, Silver, etc.)
- Set purity levels (24K, 22K, etc.)

### ✅ **Transactions**
- Record item inward transactions
- Record sales
- View transaction history

### ✅ **Dashboard**
- View business statistics
- Quick action buttons
- Recent activity

## 💾 **Important: Data Storage**

**Your data is stored in the web browser**, not in a database:

### ✅ **Advantages**:
- No database setup required
- Works immediately
- Fast and responsive
- No server maintenance

### ⚠️ **Important Notes**:
- Data stays in the browser you're using
- Don't clear browser data/cookies
- Use the same browser each time
- Export data regularly for backup

## 📤 **Backup Your Data**

### To Export Data:
1. **Go to** History section
2. **Click** "Export" button
3. **Save** the JSON file
4. **Keep** this file safe

### To Import Data:
1. **Go to** History section
2. **Click** "Import" button
3. **Select** your saved JSON file

## 🌐 **Access from Other Computers**

### On Same Network:
1. **Find your computer's IP** (run `ipconfig` in Command Prompt)
2. **Other computers can access**: `http://YOUR_IP/jewelry-tracker/`
3. **Example**: `http://*************/jewelry-tracker/`

## 🔧 **Troubleshooting**

### Problem: Website doesn't load
**Solution**: 
- Check WAMP Apache is running (green)
- Try: `http://localhost/jewelry-tracker/index.static.html`

### Problem: Can't login
**Solution**: 
- Use exact credentials: `<EMAIL>` / `Admin@1234`
- Check caps lock is off

### Problem: Data disappeared
**Solution**: 
- Don't clear browser data
- Import from your backup JSON file

### Problem: Slow performance
**Solution**: 
- Use Chrome or Firefox
- Close other browser tabs
- Restart browser

## 📞 **Support**

### Before Asking for Help:
- ✅ WAMP Apache is running (green)
- ✅ Using correct URL: `http://localhost/jewelry-tracker/`
- ✅ Using correct login: `<EMAIL>` / `Admin@1234`
- ✅ Same browser each time
- ✅ Haven't cleared browser data

## 🎯 **Quick Reference**

**Website Location**: `C:\wamp64\www\jewelry-tracker\`
**Access URL**: `http://localhost/jewelry-tracker/`
**Login**: `<EMAIL>` / `Admin@1234`
**Data Storage**: Browser localStorage
**Backup**: Export from History section

---

## ✅ **Success Checklist**

Your website is working correctly when:
- [ ] Login page loads at `http://localhost/jewelry-tracker/`
- [ ] Can login with admin credentials
- [ ] Dashboard shows with sample data
- [ ] Can add new customers
- [ ] Can add new items
- [ ] Data persists when refreshing page
- [ ] All menu items work (Masters, Transactions, History)

**🎉 Congratulations! Your Jewelry Tracker website is ready to use!**

---

**Need the database version instead?** 
Contact your developer to set up the MySQL database version for multi-user access and permanent data storage.
