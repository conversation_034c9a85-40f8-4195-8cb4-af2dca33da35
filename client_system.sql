-- Jewelry Tracker Database Setup Script
-- Run this script in phpMyAdmin or MySQL command line

-- Create database
CREATE DATABASE IF NOT EXISTS jewelry_tracker;
USE jewelry_tracker;

-- Create user (optional - you can use root or existing user)
-- CREATE USER 'jewelry_admin'@'localhost' IDENTIFIED BY 'jewelry123';
-- GRANT ALL PRIVILEGES ON jewelry_tracker.* TO 'jewelry_admin'@'localhost';
-- FLUSH PRIVILEGES;

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  balance DECIMAL(15,2) DEFAULT 0.00,
  mobile VARCHAR(20),
  email VARCHAR(255),
  address TEXT,
  remarks TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by <PERSON><PERSON><PERSON><PERSON>(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHA<PERSON>(255),
  INDEX idx_name (name),
  INDEX idx_status (status)
);

-- Items table
CREATE TABLE IF NOT EXISTS items (
  id VARCHAR(50) PRIMARY KEY,
  number VARCHAR(100) NOT NULL UNIQUE,
  description TEXT NOT NULL,
  remarks TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  INDEX idx_number (number),
  INDEX idx_status (status)
);

-- Workers table
CREATE TABLE IF NOT EXISTS workers (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  balance DECIMAL(15,2) DEFAULT 0.00,
  remarks TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  INDEX idx_name (name),
  INDEX idx_status (status)
);

-- Metals table
CREATE TABLE IF NOT EXISTS metals (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  INDEX idx_name (name),
  INDEX idx_status (status)
);

-- Purities table
CREATE TABLE IF NOT EXISTS purities (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  percentage DECIMAL(5,2),
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  INDEX idx_name (name),
  INDEX idx_status (status)
);

-- Item Inwards table
CREATE TABLE IF NOT EXISTS item_inwards (
  id VARCHAR(50) PRIMARY KEY,
  date DATE NOT NULL,
  customer_id VARCHAR(50) NOT NULL,
  item_id VARCHAR(50) NOT NULL,
  melting VARCHAR(100),
  gross_weight DECIMAL(10,3),
  st VARCHAR(100),
  en VARCHAR(100),
  thd VARCHAR(100),
  piece_count INT,
  net_weight DECIMAL(10,3),
  comments TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE RESTRICT,
  INDEX idx_date (date),
  INDEX idx_customer (customer_id),
  INDEX idx_item (item_id),
  INDEX idx_status (status)
);

-- Item Outwards table
CREATE TABLE IF NOT EXISTS item_outwards (
  id VARCHAR(50) PRIMARY KEY,
  date DATE NOT NULL,
  customer_id VARCHAR(50) NOT NULL,
  item_id VARCHAR(50) NOT NULL,
  melting VARCHAR(100),
  gross_weight DECIMAL(10,3),
  st VARCHAR(100),
  en VARCHAR(100),
  thd VARCHAR(100),
  piece_count INT,
  net_weight DECIMAL(10,3),
  comments TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE RESTRICT,
  INDEX idx_date (date),
  INDEX idx_customer (customer_id),
  INDEX idx_item (item_id),
  INDEX idx_status (status)
);

-- Metal Inwards table
CREATE TABLE IF NOT EXISTS metal_inwards (
  id VARCHAR(50) PRIMARY KEY,
  date DATE NOT NULL,
  customer_id VARCHAR(50) NOT NULL,
  metal_id VARCHAR(50) NOT NULL,
  purity_id VARCHAR(50),
  weight DECIMAL(10,3),
  rate DECIMAL(10,2),
  amount DECIMAL(15,2),
  comments TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (metal_id) REFERENCES metals(id) ON DELETE RESTRICT,
  FOREIGN KEY (purity_id) REFERENCES purities(id) ON DELETE RESTRICT,
  INDEX idx_date (date),
  INDEX idx_customer (customer_id),
  INDEX idx_metal (metal_id),
  INDEX idx_status (status)
);

-- Metal Outwards table
CREATE TABLE IF NOT EXISTS metal_outwards (
  id VARCHAR(50) PRIMARY KEY,
  date DATE NOT NULL,
  customer_id VARCHAR(50) NOT NULL,
  metal_id VARCHAR(50) NOT NULL,
  purity_id VARCHAR(50),
  weight DECIMAL(10,3),
  rate DECIMAL(10,2),
  amount DECIMAL(15,2),
  comments TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (metal_id) REFERENCES metals(id) ON DELETE RESTRICT,
  FOREIGN KEY (purity_id) REFERENCES purities(id) ON DELETE RESTRICT,
  INDEX idx_date (date),
  INDEX idx_customer (customer_id),
  INDEX idx_metal (metal_id),
  INDEX idx_status (status)
);

-- Sales table
CREATE TABLE IF NOT EXISTS sales (
  id VARCHAR(50) PRIMARY KEY,
  date DATE NOT NULL,
  customer_id VARCHAR(50) NOT NULL,
  item_id VARCHAR(50) NOT NULL,
  amount DECIMAL(15,2),
  net_weight DECIMAL(10,3),
  remarks TEXT,
  status ENUM('Active', 'Inactive') DEFAULT 'Active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by VARCHAR(255),
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE RESTRICT,
  FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE RESTRICT,
  INDEX idx_date (date),
  INDEX idx_customer (customer_id),
  INDEX idx_item (item_id),
  INDEX idx_status (status)
);

-- Insert sample data
INSERT INTO customers (id, name, balance, mobile, email, status, created_by) VALUES
('CUSTOMER001', 'John Doe', 5000.00, '9876543210', '<EMAIL>', 'Active', '<EMAIL>'),
('CUSTOMER002', 'Jane Smith', 3000.00, '9876543211', '<EMAIL>', 'Active', '<EMAIL>'),
('CUSTOMER003', 'Bob Johnson', 2000.00, '9876543212', '<EMAIL>', 'Active', '<EMAIL>');

INSERT INTO items (id, number, description, status, created_by) VALUES
('ITEM001', 'ITM001', 'Gold Ring', 'Active', '<EMAIL>'),
('ITEM002', 'ITM002', 'Silver Necklace', 'Active', '<EMAIL>'),
('ITEM003', 'ITM003', 'Diamond Earrings', 'Active', '<EMAIL>');

INSERT INTO workers (id, name, balance, status, created_by) VALUES
('WORKER001', 'Ravi Kumar', 1500.00, 'Active', '<EMAIL>'),
('WORKER002', 'Suresh Patel', 2000.00, 'Active', '<EMAIL>'),
('WORKER003', 'Amit Singh', 1800.00, 'Active', '<EMAIL>');

INSERT INTO metals (id, name, description, status, created_by) VALUES
('METAL001', 'Gold', '24K Gold', 'Active', '<EMAIL>'),
('METAL002', 'Silver', '925 Silver', 'Active', '<EMAIL>'),
('METAL003', 'Platinum', 'Pure Platinum', 'Active', '<EMAIL>');

INSERT INTO purities (id, name, percentage, status, created_by) VALUES
('PURITY001', '24K', 99.99, 'Active', '<EMAIL>'),
('PURITY002', '22K', 91.67, 'Active', '<EMAIL>'),
('PURITY003', '18K', 75.00, 'Active', '<EMAIL>'),
('PURITY004', '925', 92.50, 'Active', '<EMAIL>');

-- Sample transactions
INSERT INTO item_inwards (id, date, customer_id, item_id, gross_weight, net_weight, piece_count, status, created_by) VALUES
('INWARD001', '2024-01-15', 'CUSTOMER001', 'ITEM001', 10.500, 9.800, 1, 'Active', '<EMAIL>'),
('INWARD002', '2024-01-16', 'CUSTOMER002', 'ITEM002', 25.300, 24.100, 1, 'Active', '<EMAIL>');

INSERT INTO sales (id, date, customer_id, item_id, amount, net_weight, status, created_by) VALUES
('SALE001', '2024-01-20', 'CUSTOMER001', 'ITEM001', 45000.00, 9.800, 'Active', '<EMAIL>'),
('SALE002', '2024-01-21', 'CUSTOMER003', 'ITEM003', 85000.00, 5.200, 'Active', '<EMAIL>');

-- Show tables created
SHOW TABLES;
