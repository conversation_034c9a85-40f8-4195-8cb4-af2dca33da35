@echo off
title Jewelry Tracker MySQL Server
color 0A
echo ========================================
echo    Jewelry Tracker - MySQL Database Version
echo ========================================
echo.

echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js is not installed!
    echo.
    echo Please install Node.js from https://nodejs.org/
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed successfully.
echo.

echo [2/5] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: npm is not available!
    echo Please reinstall Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ npm is available.
echo.

echo [3/5] Installing/Updating dependencies...
echo This may take a few minutes on first run...
call npm install
if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to install dependencies!
    echo.
    echo Try running as Administrator or check internet connection.
    echo.
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully.
echo.

echo [4/5] Checking WAMP/MySQL connection...
echo Make sure WAMP server is running with MySQL service started.
echo This version stores data in MySQL database, not browser storage.
echo.

echo [5/5] Starting Jewelry Tracker Server...
echo.
echo ========================================
echo  🚀 Server will start on: http://localhost:3000
echo  📧 Login: <EMAIL>
echo  🔑 Password: Admin@1234
echo  💾 Data Storage: MySQL Database
echo  🗄️  Database: jewelry_tracker
echo ========================================
echo.
echo 💡 Tips:
echo  - Keep this window open while using the application
echo  - Press Ctrl+C to stop the server
echo  - If port 3000 is busy, the server will try another port
echo.
echo Starting server now...
echo.

call npm run dev

echo.
echo ========================================
echo Server has stopped.
echo.
pause
