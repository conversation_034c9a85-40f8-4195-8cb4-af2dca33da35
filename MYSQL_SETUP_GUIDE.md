# Jewelry Tracker - MySQL Database Setup Guide

## Prerequisites
1. **XAMPP** or **WAMP** server installed on your system
2. **Node.js** (version 16 or higher)
3. **npm** package manager

## Database Configuration

### Database Details:
- **Database Name**: `jewelry_tracker`
- **Username**: `jewelry_admin` (or use `root`)
- **Password**: `jewelry123` (or your root password)
- **Host**: `localhost`
- **Port**: `3306`

## Step-by-Step Setup Instructions

### 1. Install and Start XAMPP/WAMP
1. Download and install XAMPP from https://www.apachefriends.org/
2. Start the XAMPP Control Panel
3. Start **Apache** and **MySQL** services
4. Ensure both services are running (green status)

### 2. Create Database and User
1. Open your web browser and go to: `http://localhost/phpmyadmin`
2. Click on "SQL" tab
3. Copy and paste the entire content from `database-setup.sql` file
4. Click "Go" to execute the script
5. This will create:
   - Database: `jewelry_tracker`
   - All required tables with sample data
   - Proper indexes and foreign key relationships

### 3. Configure Database Connection
The database configuration is in `server/database.ts`:

```typescript
export const dbConfig = {
  host: 'localhost',
  user: 'jewelry_admin',  // Change to 'root' if using root user
  password: 'jewelry123', // Change to your MySQL password
  database: 'jewelry_tracker',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};
```

**Important**: If you're using the root user or different credentials, update these values in `server/database.ts`

### 4. Install Dependencies
Open terminal/command prompt in the project directory and run:

```bash
npm install
```

This will install all required dependencies including `mysql2` for database connectivity.

### 5. Fix the tsx Command Issue
The error you're getting is because `tsx` is not globally installed. Run:

```bash
npm install -g tsx
```

Or alternatively, you can run the app using:

```bash
npx tsx server/index.ts
```

### 6. Start the Application
Run the development server:

```bash
npm run dev
```

The application will start on: `http://localhost:3000`

## Database Tables Created

The setup script creates the following tables:

1. **customers** - Customer master data
2. **items** - Item master data  
3. **workers** - Worker master data
4. **metals** - Metal types (Gold, Silver, etc.)
5. **purities** - Metal purity levels (24K, 22K, etc.)
6. **item_inwards** - Item inward transactions
7. **item_outwards** - Item outward transactions
8. **metal_inwards** - Metal inward transactions
9. **metal_outwards** - Metal outward transactions
10. **sales** - Sales transactions

## Sample Data Included

The script includes sample data for:
- 3 Customers
- 3 Items
- 3 Workers
- 3 Metals
- 4 Purity levels
- 2 Item inward transactions
- 2 Sales transactions

## Login Credentials

- **Username**: `<EMAIL>`
- **Password**: `Admin@1234`

## Features Available

✅ **Masters Management**:
- Customer Master (CRUD operations)
- Item Master (CRUD operations)
- Worker Master (CRUD operations)
- Metal Master (CRUD operations)
- Purity Master (CRUD operations)

✅ **Transactions**:
- Item Inward (CRUD operations)
- Sales (CRUD operations)
- Item Outward (placeholder)
- Metal Inward/Outward (placeholder)

✅ **Dashboard**:
- Statistics cards
- Recent transactions
- Quick action buttons

✅ **History & Reports**:
- Transaction history
- Date-wise filtering
- Export functionality (placeholder)

## Troubleshooting

### Common Issues:

1. **"tsx: command not found"**
   ```bash
   npm install -g tsx
   ```

2. **Database connection error**
   - Ensure MySQL is running in XAMPP/WAMP
   - Check database credentials in `server/database.ts`
   - Verify database name exists

3. **Port 3000 already in use**
   - Change port in `server/index.ts` (line 61)
   - Or kill the process using port 3000

4. **Permission denied for database**
   - Use root user credentials
   - Or create the user with proper privileges

### Verify Database Connection:
1. Go to phpMyAdmin: `http://localhost/phpmyadmin`
2. Check if `jewelry_tracker` database exists
3. Verify all tables are created with data

## File Structure

```
project/
├── server/
│   ├── database.ts              # Database configuration
│   ├── services/
│   │   └── database-service.ts  # Database service layer
│   ├── routes-mysql.ts          # MySQL-based API routes
│   └── index.ts                 # Main server file
├── database-setup.sql           # Database creation script
├── MYSQL_SETUP_GUIDE.md        # This guide
└── package.json                 # Dependencies
```

## Next Steps

After successful setup:
1. Access the application at `http://localhost:3000`
2. Login with the provided credentials
3. Test all CRUD operations
4. Add your own data
5. Customize as needed for your client

## For Client Deployment

When deploying to client:
1. Provide this setup guide
2. Include the `database-setup.sql` file
3. Ensure they have XAMPP/WAMP installed
4. Guide them through the database setup
5. Update database credentials as needed

## Support

If you encounter any issues:
1. Check XAMPP/WAMP services are running
2. Verify database connection settings
3. Check browser console for errors
4. Ensure all npm dependencies are installed
