import { pgTable, text, serial, integer, decimal, timestamp, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

// Master tables
export const customers = pgTable("customers", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  balance: decimal("balance", { precision: 10, scale: 2 }).default("0"),
  mobile: text("mobile"),
  email: text("email"),
  address: text("address"),
  remarks: text("remarks"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const items = pgTable("items", {
  id: text("id").primaryKey(),
  number: text("number").notNull(),
  description: text("description").notNull(),
  remarks: text("remarks"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const workers = pgTable("workers", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  balance: decimal("balance", { precision: 10, scale: 2 }).default("0"),
  remarks: text("remarks"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const metals = pgTable("metals", {
  id: text("id").primaryKey(),
  description: text("description").notNull(),
  remarks: text("remarks"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const purity = pgTable("purity", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  metalType: text("metal_type").notNull(),
  description: text("description"),
  remarks: text("remarks"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

// Transaction tables
export const itemInward = pgTable("item_inward", {
  id: text("id").primaryKey(),
  date: text("date").notNull(),
  customerId: text("customer_id").notNull(),
  itemId: text("item_id").notNull(),
  melting: text("melting"),
  grossWeight: decimal("gross_weight", { precision: 10, scale: 2 }),
  st: text("st"),
  en: text("en"),
  thd: text("thd"),
  pieceCount: integer("piece_count"),
  netWeight: decimal("net_weight", { precision: 10, scale: 2 }),
  comments: text("comments"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const itemOutward = pgTable("item_outward", {
  id: text("id").primaryKey(),
  date: text("date").notNull(),
  customerId: text("customer_id").notNull(),
  itemId: text("item_id").notNull(),
  melting: text("melting"),
  grossWeight: decimal("gross_weight", { precision: 10, scale: 2 }),
  st: text("st"),
  en: text("en"),
  thd: text("thd"),
  pieceCount: integer("piece_count"),
  netWeight: decimal("net_weight", { precision: 10, scale: 2 }),
  comments: text("comments"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const metalInward = pgTable("metal_inward", {
  id: text("id").primaryKey(),
  date: text("date").notNull(),
  customerId: text("customer_id").notNull(),
  metalId: text("metal_id").notNull(),
  weight: decimal("weight", { precision: 10, scale: 2 }),
  purityId: text("purity_id"),
  rate: decimal("rate", { precision: 10, scale: 2 }),
  pieceCount: integer("piece_count"),
  netWeight: decimal("net_weight", { precision: 10, scale: 2 }),
  comments: text("comments"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const metalOutward = pgTable("metal_outward", {
  id: text("id").primaryKey(),
  date: text("date").notNull(),
  customerId: text("customer_id").notNull(),
  metalId: text("metal_id").notNull(),
  weight: decimal("weight", { precision: 10, scale: 2 }),
  purityId: text("purity_id"),
  rate: decimal("rate", { precision: 10, scale: 2 }),
  pieceCount: integer("piece_count"),
  netWeight: decimal("net_weight", { precision: 10, scale: 2 }),
  comments: text("comments"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

export const sales = pgTable("sales", {
  id: text("id").primaryKey(),
  date: text("date").notNull(),
  customerId: text("customer_id").notNull(),
  itemId: text("item_id").notNull(),
  melting: text("melting"),
  grossWeight: decimal("gross_weight", { precision: 10, scale: 2 }),
  st: text("st"),
  en: text("en"),
  thd: text("thd"),
  pieceCount: integer("piece_count"),
  netWeight: decimal("net_weight", { precision: 10, scale: 2 }),
  amount: decimal("amount", { precision: 10, scale: 2 }),
  comments: text("comments"),
  status: text("status").default("Active"),
  createdAt: timestamp("created_at").defaultNow(),
  createdBy: text("created_by").default("<EMAIL>"),
  updatedAt: timestamp("updated_at"),
  updatedBy: text("updated_by"),
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({ id: true });
export const insertCustomerSchema = createInsertSchema(customers).omit({ createdAt: true, updatedAt: true });
export const insertItemSchema = createInsertSchema(items).omit({ createdAt: true, updatedAt: true });
export const insertWorkerSchema = createInsertSchema(workers).omit({ createdAt: true, updatedAt: true });
export const insertMetalSchema = createInsertSchema(metals).omit({ createdAt: true, updatedAt: true });
export const insertPuritySchema = createInsertSchema(purity).omit({ createdAt: true, updatedAt: true });
export const insertItemInwardSchema = createInsertSchema(itemInward).omit({ createdAt: true, updatedAt: true });
export const insertItemOutwardSchema = createInsertSchema(itemOutward).omit({ createdAt: true, updatedAt: true });
export const insertMetalInwardSchema = createInsertSchema(metalInward).omit({ createdAt: true, updatedAt: true });
export const insertMetalOutwardSchema = createInsertSchema(metalOutward).omit({ createdAt: true, updatedAt: true });
export const insertSalesSchema = createInsertSchema(sales).omit({ createdAt: true, updatedAt: true });

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Customer = typeof customers.$inferSelect;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;
export type Item = typeof items.$inferSelect;
export type InsertItem = z.infer<typeof insertItemSchema>;
export type Worker = typeof workers.$inferSelect;
export type InsertWorker = z.infer<typeof insertWorkerSchema>;
export type Metal = typeof metals.$inferSelect;
export type InsertMetal = z.infer<typeof insertMetalSchema>;
export type Purity = typeof purity.$inferSelect;
export type InsertPurity = z.infer<typeof insertPuritySchema>;
export type ItemInward = typeof itemInward.$inferSelect;
export type InsertItemInward = z.infer<typeof insertItemInwardSchema>;
export type ItemOutward = typeof itemOutward.$inferSelect;
export type InsertItemOutward = z.infer<typeof insertItemOutwardSchema>;
export type MetalInward = typeof metalInward.$inferSelect;
export type InsertMetalInward = z.infer<typeof insertMetalInwardSchema>;
export type MetalOutward = typeof metalOutward.$inferSelect;
export type InsertMetalOutward = z.infer<typeof insertMetalOutwardSchema>;
export type Sales = typeof sales.$inferSelect;
export type InsertSales = z.infer<typeof insertSalesSchema>;
