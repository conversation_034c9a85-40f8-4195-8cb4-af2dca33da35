# Jewelry Tracker PowerShell Startup Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Jewelry Tracker Application" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Node.js installation
Write-Host "[1/5] Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js is installed: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Node.js is not installed!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Write-Host "After installation, restart this script." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check npm installation
Write-Host "[2/5] Checking npm installation..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "✅ npm is available: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: npm is not available!" -ForegroundColor Red
    Write-Host "Please reinstall Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Install dependencies
Write-Host "[3/5] Installing/Updating dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes on first run..." -ForegroundColor Gray

try {
    npm install
    Write-Host "✅ Dependencies installed successfully." -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Failed to install dependencies!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Try running as Administrator or check internet connection." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check WAMP/MySQL
Write-Host "[4/5] Checking WAMP/MySQL connection..." -ForegroundColor Yellow
Write-Host "Make sure WAMP server is running with MySQL service started." -ForegroundColor Gray
Write-Host ""

# Start server
Write-Host "[5/5] Starting Jewelry Tracker Server..." -ForegroundColor Yellow
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  🚀 Server will start on: http://localhost:3000" -ForegroundColor White
Write-Host "  📧 Login: <EMAIL>" -ForegroundColor White
Write-Host "  🔑 Password: Admin@1234" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor Yellow
Write-Host "  - Keep this window open while using the application" -ForegroundColor Gray
Write-Host "  - Press Ctrl+C to stop the server" -ForegroundColor Gray
Write-Host "  - If port 3000 is busy, the server will try another port" -ForegroundColor Gray
Write-Host ""
Write-Host "Starting server now..." -ForegroundColor Green
Write-Host ""

try {
    npm run dev
} catch {
    Write-Host ""
    Write-Host "❌ Server encountered an error." -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Server has stopped." -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to exit"
