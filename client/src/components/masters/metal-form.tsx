import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { insertMetalSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Metal } from "@shared/schema";

interface MetalFormProps {
  metal?: Metal;
  onClose: () => void;
}

export function MetalForm({ metal, onClose }: MetalFormProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const form = useForm({
    resolver: zodResolver(insertMetalSchema),
    defaultValues: {
      id: metal?.id || "",
      description: metal?.description || "",
      remarks: metal?.remarks || "",
    },
  });

  const mutation = useMutation({
    mutationFn: (data: any) => {
      if (metal) {
        return apiRequest("PUT", `/api/metals/${metal.id}`, data);
      }
      return apiRequest("POST", "/api/metals", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metals"] });
      toast({
        title: metal ? "Metal updated" : "Metal created",
        description: metal ? "Metal has been updated successfully." : "New metal has been created successfully.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save metal. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: any) => {
    mutation.mutate(data);
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>{metal ? "Edit Metal" : "Add New Metal"}</DialogTitle>
      </DialogHeader>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Metal ID</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Auto-generated" disabled={!metal} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter metal description" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="remarks"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Remarks</FormLabel>
                <FormControl>
                  <Textarea {...field} placeholder="Additional remarks" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? "Saving..." : metal ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
