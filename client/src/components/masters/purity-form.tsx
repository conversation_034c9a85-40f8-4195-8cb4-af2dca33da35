import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { insertPuritySchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Purity } from "@shared/schema";

interface PurityFormProps {
  purity?: Purity;
  onClose: () => void;
}

export function PurityForm({ purity, onClose }: PurityFormProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const form = useForm({
    resolver: zodResolver(insertPuritySchema),
    defaultValues: {
      id: purity?.id || "",
      name: purity?.name || "",
      metalType: purity?.metalType || "",
      description: purity?.description || "",
      remarks: purity?.remarks || "",
    },
  });

  const mutation = useMutation({
    mutationFn: (data: any) => {
      if (purity) {
        return apiRequest("PUT", `/api/purities/${purity.id}`, data);
      }
      return apiRequest("POST", "/api/purities", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/purities"] });
      toast({
        title: purity ? "Purity updated" : "Purity created",
        description: purity ? "Purity has been updated successfully." : "New purity has been created successfully.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save purity. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: any) => {
    mutation.mutate(data);
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>{purity ? "Edit Purity" : "Add New Purity"}</DialogTitle>
      </DialogHeader>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purity ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Auto-generated" disabled={!purity} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purity Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter purity name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="metalType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Metal Type</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter metal type" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="Enter description" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="remarks"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Remarks</FormLabel>
                <FormControl>
                  <Textarea {...field} placeholder="Additional remarks" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? "Saving..." : purity ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
