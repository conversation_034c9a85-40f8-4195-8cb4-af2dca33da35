import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { insertItemOutwardSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { ItemOutward } from "@shared/schema";

interface ItemOutwardFormProps {
  itemOutward?: ItemOutward;
  onClose: () => void;
}

export function ItemOutwardForm({ itemOutward, onClose }: ItemOutwardFormProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: customers = [] } = useQuery({ queryKey: ["/api/customers"] });
  const { data: items = [] } = useQuery({ queryKey: ["/api/items"] });

  const form = useForm({
    resolver: zodResolver(insertItemOutwardSchema),
    defaultValues: {
      id: itemOutward?.id || "",
      date: itemOutward?.date || new Date().toISOString().split('T')[0],
      customerId: itemOutward?.customerId || "",
      itemId: itemOutward?.itemId || "",
      melting: itemOutward?.melting || "",
      grossWeight: itemOutward?.grossWeight || "",
      st: itemOutward?.st || "",
      en: itemOutward?.en || "",
      thd: itemOutward?.thd || "",
      pieceCount: itemOutward?.pieceCount || 0,
      netWeight: itemOutward?.netWeight || "",
      comments: itemOutward?.comments || "",
    },
  });

  const mutation = useMutation({
    mutationFn: (data: any) => {
      if (itemOutward) {
        return apiRequest("PUT", `/api/item-outwards/${itemOutward.id}`, data);
      }
      return apiRequest("POST", "/api/item-outwards", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/item-outwards"] });
      toast({
        title: itemOutward ? "Item outward updated" : "Item outward created",
        description: itemOutward ? "Item outward has been updated successfully." : "New item outward has been created successfully.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save item outward. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: any) => {
    mutation.mutate(data);
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>{itemOutward ? "Edit Item Outward" : "Add Item Outward Transaction"}</DialogTitle>
      </DialogHeader>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Transaction ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Auto-generated" disabled={!itemOutward} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Outward Date</FormLabel>
                  <FormControl>
                    <Input {...field} type="date" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select customer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {customers.map((customer: any) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="itemId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Item</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select item" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {items.map((item: any) => (
                        <SelectItem key={item.id} value={item.id}>
                          {item.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="melting"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Melting</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Melting details" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="grossWeight"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Gross Weight (g)</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" step="0.01" placeholder="0.00" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="st"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ST</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="ST value" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="en"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>EN</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="EN value" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="thd"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>THD</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="THD value" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="pieceCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Piece Count</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" placeholder="0" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="netWeight"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Net Weight (g)</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" step="0.01" placeholder="0.00" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="comments"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Comments</FormLabel>
                <FormControl>
                  <Textarea {...field} placeholder="Transaction comments" rows={3} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? "Saving..." : itemOutward ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
