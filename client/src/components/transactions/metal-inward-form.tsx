import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { insertMetalInwardSchema } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { MetalInward } from "@shared/schema";

interface MetalInwardFormProps {
  metalInward?: MetalInward;
  onClose: () => void;
}

export function MetalInwardForm({ metalInward, onClose }: MetalInwardFormProps) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: customers = [] } = useQuery({ queryKey: ["/api/customers"] });
  const { data: metals = [] } = useQuery({ queryKey: ["/api/metals"] });
  const { data: purities = [] } = useQuery({ queryKey: ["/api/purities"] });

  const form = useForm({
    resolver: zodResolver(insertMetalInwardSchema),
    defaultValues: {
      id: metalInward?.id || "",
      date: metalInward?.date || new Date().toISOString().split('T')[0],
      customerId: metalInward?.customerId || "",
      metalId: metalInward?.metalId || "",
      weight: metalInward?.weight || "",
      purityId: metalInward?.purityId || "",
      rate: metalInward?.rate || "",
      pieceCount: metalInward?.pieceCount || 0,
      netWeight: metalInward?.netWeight || "",
      comments: metalInward?.comments || "",
      status: metalInward?.status || "Active",
    },
  });

  const mutation = useMutation({
    mutationFn: (data: any) => {
      if (metalInward) {
        return apiRequest("PUT", `/api/metal-inwards/${metalInward.id}`, data);
      }
      return apiRequest("POST", "/api/metal-inwards", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metal-inwards"] });
      toast({
        title: metalInward ? "Metal inward updated" : "Metal inward created",
        description: metalInward ? "Metal inward has been updated successfully." : "New metal inward has been created successfully.",
      });
      onClose();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to save metal inward. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: any) => {
    mutation.mutate(data);
  };

  return (
    <>
      <DialogHeader>
        <DialogTitle>{metalInward ? "Edit Metal Inward" : "Add Metal Inward Transaction"}</DialogTitle>
      </DialogHeader>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Metal Inward ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Auto-generated" disabled={!metalInward} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Inward Date</FormLabel>
                  <FormControl>
                    <Input {...field} type="date" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="customerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select customer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {customers.map((customer: any) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metalId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Metal</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select metal" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {metals.map((metal: any) => (
                        <SelectItem key={metal.id} value={metal.id}>
                          {metal.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="weight"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Weight (g)</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" step="0.01" placeholder="0.00" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="purityId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Purity</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select purity" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {purities.map((purity: any) => (
                        <SelectItem key={purity.id} value={purity.id}>
                          {purity.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="rate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rate</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" step="0.01" placeholder="0.00" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="pieceCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Piece Count</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" placeholder="0" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="netWeight"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Net Weight (g)</FormLabel>
                <FormControl>
                  <Input {...field} type="number" step="0.01" placeholder="0.00" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="comments"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Comments</FormLabel>
                <FormControl>
                  <Textarea {...field} placeholder="Transaction comments" rows={3} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? "Saving..." : metalInward ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
