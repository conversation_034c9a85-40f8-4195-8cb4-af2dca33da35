import { Dialog, DialogContent } from "@/components/ui/dialog";
import { CustomerForm } from "@/components/masters/customer-form";
import { ItemForm } from "@/components/masters/item-form";
import { WorkerForm } from "@/components/masters/worker-form";

interface QuickActionFormsProps {
  type: "customer" | "item" | "worker" | null;
  isOpen: boolean;
  onClose: () => void;
}

export function QuickActionForms({ type, isOpen, onClose }: QuickActionFormsProps) {
  const renderForm = () => {
    switch (type) {
      case "customer":
        return <CustomerForm onClose={onClose} />;
      case "item":
        return <ItemForm onClose={onClose} />;
      case "worker":
        return <WorkerForm onClose={onClose} />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        {renderForm()}
      </DialogContent>
    </Dialog>
  );
}