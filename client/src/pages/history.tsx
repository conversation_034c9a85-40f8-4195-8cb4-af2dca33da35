import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  ArrowDown, 
  ArrowUp, 
  Coins, 
  FileText, 
  TrendingUp,
  Calendar,
  Filter,
  Download
} from "lucide-react";
import type { ItemInward, ItemOutward, MetalInward, MetalOutward, Sales } from "@shared/schema";

type ReportType = "item-inward" | "item-outward" | "metal-inward" | "metal-outward" | "sales";

export default function History() {
  const [activeReport, setActiveReport] = useState<ReportType | null>(null);
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");

  // Queries
  const { data: customers = [] } = useQuery({ queryKey: ["/api/customers"] });
  const { data: items = [] } = useQuery({ queryKey: ["/api/items"] });
  const { data: purities = [] } = useQuery({ queryKey: ["/api/purities"] });
  
  const { data: itemInwards = [] } = useQuery({ queryKey: ["/api/item-inwards"] });
  const { data: itemOutwards = [] } = useQuery({ queryKey: ["/api/item-outwards"] });
  const { data: metalInwards = [] } = useQuery({ queryKey: ["/api/metal-inwards"] });
  const { data: sales = [] } = useQuery({ queryKey: ["/api/sales"] });

  const getCustomerName = (id: string) => {
    const customer = customers.find((c: any) => c.id === id);
    return customer?.name || "Unknown";
  };

  const getItemName = (id: string) => {
    const item = items.find((i: any) => i.id === id);
    return item?.description || "Unknown";
  };

  const getPurityName = (id: string) => {
    const purity = purities.find((p: any) => p.id === id);
    return purity?.name || "Unknown";
  };

  const filterDataByDate = (data: any[]) => {
    if (!dateFrom && !dateTo) return data;
    
    return data.filter((item: any) => {
      const itemDate = new Date(item.date);
      const fromDate = dateFrom ? new Date(dateFrom) : null;
      const toDate = dateTo ? new Date(dateTo) : null;
      
      if (fromDate && itemDate < fromDate) return false;
      if (toDate && itemDate > toDate) return false;
      return true;
    });
  };

  // Column definitions for reports
  const itemInwardColumns: ColumnDef<ItemInward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { 
      accessorKey: "itemId", 
      header: "Item",
      cell: ({ row }) => getItemName(row.getValue("itemId"))
    },
    { accessorKey: "grossWeight", header: "Gross Weight (g)" },
    { accessorKey: "netWeight", header: "Net Weight (g)" },
    { accessorKey: "comments", header: "Comments" },
  ];

  const itemOutwardColumns: ColumnDef<ItemOutward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { 
      accessorKey: "itemId", 
      header: "Item",
      cell: ({ row }) => getItemName(row.getValue("itemId"))
    },
    { accessorKey: "grossWeight", header: "Gross Weight (g)" },
    { accessorKey: "netWeight", header: "Net Weight (g)" },
    { accessorKey: "comments", header: "Comments" },
  ];

  const metalInwardColumns: ColumnDef<MetalInward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { accessorKey: "weight", header: "Weight (g)" },
    { 
      accessorKey: "purityId", 
      header: "Purity",
      cell: ({ row }) => getPurityName(row.getValue("purityId"))
    },
    { accessorKey: "rate", header: "Rate" },
    { accessorKey: "netWeight", header: "Net Weight (g)" },
  ];

  const salesColumns: ColumnDef<Sales>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { 
      accessorKey: "itemId", 
      header: "Item",
      cell: ({ row }) => getItemName(row.getValue("itemId"))
    },
    { accessorKey: "grossWeight", header: "Gross Weight (g)" },
    { accessorKey: "netWeight", header: "Net Weight (g)" },
    { accessorKey: "amount", header: "Amount", cell: ({ row }) => `₹${row.getValue("amount")}` },
  ];

  const getReportData = (reportType: ReportType) => {
    switch (reportType) {
      case "item-inward":
        return filterDataByDate(itemInwards);
      case "item-outward":
        return filterDataByDate(itemOutwards);
      case "metal-inward":
        return filterDataByDate(metalInwards);
      case "metal-outward":
        return []; // No metal outward data in schema
      case "sales":
        return filterDataByDate(sales);
      default:
        return [];
    }
  };

  const getReportColumns = (reportType: ReportType) => {
    switch (reportType) {
      case "item-inward":
        return itemInwardColumns;
      case "item-outward":
        return itemOutwardColumns;
      case "metal-inward":
        return metalInwardColumns;
      case "metal-outward":
        return metalInwardColumns; // Same structure
      case "sales":
        return salesColumns;
      default:
        return [];
    }
  };

  const exportReport = (reportType: ReportType) => {
    const data = getReportData(reportType);
    if (data.length === 0) return;
    
    const columns = getReportColumns(reportType);
    const headers = columns.map(col => col.header).join(',');
    
    const rows = data.map(row => {
      return columns.map(col => {
        const value = (row as any)[col.accessorKey as string];
        return `"${value || ''}"`;
      }).join(',');
    }).join('\n');

    const csv = `${headers}\n${rows}`;
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${reportType}_report_${Date.now()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const reports = [
    {
      id: "item-inward",
      title: "Item Inward Report",
      description: "View all item inward transactions with detailed filtering options.",
      icon: ArrowDown,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      id: "item-outward",
      title: "Item Outward Report", 
      description: "View all item outward transactions with detailed filtering options.",
      icon: ArrowUp,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      id: "metal-inward",
      title: "Metal Inward Report",
      description: "View all metal inward transactions with purity and rate details.",
      icon: Coins,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
    },
    {
      id: "metal-outward",
      title: "Metal Outward Report",
      description: "View all metal outward transactions with detailed analysis.",
      icon: Coins,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      id: "sales",
      title: "Sales Report",
      description: "Comprehensive sales analysis with customer and item breakdowns.",
      icon: TrendingUp,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">History & Reports</h1>
        <p className="text-gray-600">View detailed reports and transaction history</p>
      </div>

      {!activeReport ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reports.map((report) => {
            const Icon = report.icon;
            return (
              <Card key={report.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{report.title}</h3>
                    <div className={`h-12 w-12 ${report.bgColor} rounded-lg flex items-center justify-center`}>
                      <Icon className={`h-6 w-6 ${report.color}`} />
                    </div>
                  </div>
                  <p className="text-gray-600 mb-4">{report.description}</p>
                  <Button 
                    className="w-full" 
                    onClick={() => setActiveReport(report.id as ReportType)}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  {reports.find(r => r.id === activeReport)?.title}
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveReport(null)}
                  >
                    Back to Reports
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => exportReport(activeReport)}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export CSV
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4 mb-6">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <Label htmlFor="dateFrom">From:</Label>
                  <Input
                    id="dateFrom"
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                    className="w-auto"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="dateTo">To:</Label>
                  <Input
                    id="dateTo"
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                    className="w-auto"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setDateFrom("");
                    setDateTo("");
                  }}
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Clear Filters
                </Button>
              </div>

              <DataTable
                columns={getReportColumns(activeReport)}
                data={getReportData(activeReport)}
                searchPlaceholder={`Search ${activeReport.replace('-', ' ')}...`}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
