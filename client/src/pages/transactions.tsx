import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/ui/data-table";
import { ItemInwardForm } from "@/components/transactions/item-inward-form";
import { ItemOutwardForm } from "@/components/transactions/item-outward-form";
import { MetalInwardForm } from "@/components/transactions/metal-inward-form";
import { MetalOutwardForm } from "@/components/transactions/metal-outward-form";
import { SalesForm } from "@/components/transactions/sales-form";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { MoreH<PERSON>zon<PERSON>, Edit, Trash2, ShoppingCart } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { ItemInward, ItemOutward, MetalInward, MetalOutward, Sales } from "@shared/schema";

export default function Transactions() {
  const [activeTab, setActiveTab] = useState("item-inward");
  const [editingItem, setEditingItem] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [salesDialogOpen, setSalesDialogOpen] = useState(false);
  const [prefilledSaleData, setPrefilledSaleData] = useState<any>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Queries
  const { data: customers = [] } = useQuery({ queryKey: ["/api/customers"] });
  const { data: items = [] } = useQuery({ queryKey: ["/api/items"] });
  const { data: metals = [] } = useQuery({ queryKey: ["/api/metals"] });
  const { data: purities = [] } = useQuery({ queryKey: ["/api/purities"] });

  const { data: itemInwards = [] } = useQuery({ queryKey: ["/api/item-inwards"] });
  const { data: itemOutwards = [] } = useQuery({ queryKey: ["/api/item-outwards"] });
  const { data: metalInwards = [] } = useQuery({ queryKey: ["/api/metal-inwards"] });
  const { data: metalOutwards = [] } = useQuery({ queryKey: ["/api/metal-outwards"] });
  const { data: sales = [] } = useQuery({ queryKey: ["/api/sales"] });

  // Delete mutations
  const deleteItemInward = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/item-inwards/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/item-inwards"] });
      toast({ title: "Item inward deleted successfully" });
    },
  });

  const deleteItemOutward = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/item-outwards/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/item-outwards"] });
      toast({ title: "Item outward deleted successfully" });
    },
  });

  const deleteMetalInward = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/metal-inwards/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metal-inwards"] });
      toast({ title: "Metal inward deleted successfully" });
    },
  });

  const deleteMetalOutward = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/metal-outwards/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metal-outwards"] });
      toast({ title: "Metal outward deleted successfully" });
    },
  });

  const deleteSale = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/sales/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/sales"] });
      toast({ title: "Sale deleted successfully" });
    },
  });

  const getCustomerName = (id: string) => {
    const customer = customers.find((c: any) => c.id === id);
    return customer?.name || "Unknown";
  };

  const getItemName = (id: string) => {
    const item = items.find((i: any) => i.id === id);
    return item?.description || "Unknown";
  };

  const getMetalName = (id: string) => {
    const metal = metals.find((m: any) => m.id === id);
    return metal?.description || "Unknown";
  };

  const getPurityName = (id: string) => {
    const purity = purities.find((p: any) => p.id === id);
    return purity?.name || "Unknown";
  };

  // Column definitions
  const itemInwardColumns: ColumnDef<ItemInward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { 
      accessorKey: "itemId", 
      header: "Item",
      cell: ({ row }) => getItemName(row.getValue("itemId"))
    },
    { accessorKey: "grossWeight", header: "Gross Weight" },
    { accessorKey: "netWeight", header: "Net Weight" },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCreateSaleFromTransaction(row.original, 'item-inward')}
            className="h-8 px-2"
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            Add Sale
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => deleteItemInward.mutate(row.original.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  const itemOutwardColumns: ColumnDef<ItemOutward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { 
      accessorKey: "itemId", 
      header: "Item",
      cell: ({ row }) => getItemName(row.getValue("itemId"))
    },
    { accessorKey: "grossWeight", header: "Gross Weight" },
    { accessorKey: "netWeight", header: "Net Weight" },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCreateSaleFromTransaction(row.original, 'item-outward')}
            className="h-8 px-2"
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            Add Sale
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => deleteItemOutward.mutate(row.original.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  const metalInwardColumns: ColumnDef<MetalInward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    {
      accessorKey: "customerId",
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    {
      accessorKey: "metalId",
      header: "Metal",
      cell: ({ row }) => getMetalName(row.getValue("metalId"))
    },
    { accessorKey: "weight", header: "Weight" },
    {
      accessorKey: "purityId",
      header: "Purity",
      cell: ({ row }) => getPurityName(row.getValue("purityId"))
    },
    { accessorKey: "rate", header: "Rate" },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCreateSaleFromTransaction(row.original, 'metal-inward')}
            className="h-8 px-2"
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            Add Sale
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => deleteMetalInward.mutate(row.original.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  const metalOutwardColumns: ColumnDef<MetalOutward>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    {
      accessorKey: "customerId",
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    {
      accessorKey: "metalId",
      header: "Metal",
      cell: ({ row }) => getMetalName(row.getValue("metalId"))
    },
    { accessorKey: "weight", header: "Weight" },
    {
      accessorKey: "purityId",
      header: "Purity",
      cell: ({ row }) => getPurityName(row.getValue("purityId"))
    },
    { accessorKey: "rate", header: "Rate" },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCreateSaleFromTransaction(row.original, 'metal-outward')}
            className="h-8 px-2"
          >
            <ShoppingCart className="h-4 w-4 mr-1" />
            Add Sale
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleEdit(row.original)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => deleteMetalOutward.mutate(row.original.id)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  const salesColumns: ColumnDef<Sales>[] = [
    { accessorKey: "id", header: "ID" },
    { accessorKey: "date", header: "Date" },
    { 
      accessorKey: "customerId", 
      header: "Customer",
      cell: ({ row }) => getCustomerName(row.getValue("customerId"))
    },
    { 
      accessorKey: "itemId", 
      header: "Item",
      cell: ({ row }) => getItemName(row.getValue("itemId"))
    },
    { accessorKey: "grossWeight", header: "Gross Weight" },
    { accessorKey: "netWeight", header: "Net Weight" },
    { accessorKey: "amount", header: "Amount", cell: ({ row }) => `₹${row.getValue("amount")}` },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteSale.mutate(row.original.id)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const handleEdit = (item: any) => {
    setEditingItem(item);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingItem(null);
  };

  const handleCloseSalesDialog = () => {
    setSalesDialogOpen(false);
    setPrefilledSaleData(null);
  };

  const handleCreateSaleFromTransaction = (transaction: any, transactionType: string) => {
    let saleData: any = {
      date: transaction.date,
      customerId: transaction.customerId,
    };

    // Map transaction data to sales form fields based on transaction type
    switch (transactionType) {
      case 'item-inward':
      case 'item-outward':
        saleData = {
          ...saleData,
          itemId: transaction.itemId,
          grossWeight: transaction.grossWeight,
          netWeight: transaction.netWeight,
          melting: transaction.melting,
          st: transaction.st,
          en: transaction.en,
          thd: transaction.thd,
          pieceCount: transaction.pieceCount,
        };
        break;

      case 'metal-inward':
      case 'metal-outward':
        // For metal transactions, we'll need to create a sale with metal details
        // Since sales form expects itemId, we'll pass metal info in comments
        saleData = {
          ...saleData,
          netWeight: transaction.weight || transaction.netWeight,
          pieceCount: transaction.pieceCount || 1,
          amount: transaction.rate ? (parseFloat(transaction.weight || transaction.netWeight || "0") * parseFloat(transaction.rate)).toString() : "",
          comments: `Metal Sale - ${getMetalName(transaction.metalId)} (${getPurityName(transaction.purityId)}) - Rate: ₹${transaction.rate || 'TBD'}`,
        };
        break;
    }

    setPrefilledSaleData(saleData);
    setSalesDialogOpen(true);
  };

  const renderForm = () => {
    switch (activeTab) {
      case "item-inward":
        return <ItemInwardForm itemInward={editingItem} onClose={handleCloseDialog} />;
      case "item-outward":
        return <ItemOutwardForm itemOutward={editingItem} onClose={handleCloseDialog} />;
      case "metal-inward":
        return <MetalInwardForm metalInward={editingItem} onClose={handleCloseDialog} />;
      case "metal-outward":
        return <MetalOutwardForm metalOutward={editingItem} onClose={handleCloseDialog} />;
      case "sales":
        return <SalesForm sale={editingItem} onClose={handleCloseDialog} />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Transactions</h1>
        <p className="text-gray-600">Manage item inward, outward, metal transactions, and sales</p>
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="item-inward">Item Inward</TabsTrigger>
              <TabsTrigger value="item-outward">Item Outward</TabsTrigger>
              <TabsTrigger value="metal-inward">Metal Inward</TabsTrigger>
              <TabsTrigger value="metal-outward">Metal Outward</TabsTrigger>
              <TabsTrigger value="sales">Sales</TabsTrigger>
            </TabsList>

            <TabsContent value="item-inward" className="mt-6">
              <DataTable
                columns={itemInwardColumns}
                data={itemInwards}
                searchPlaceholder="Search item inwards..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Item Inward"
              />
            </TabsContent>

            <TabsContent value="item-outward" className="mt-6">
              <DataTable
                columns={itemOutwardColumns}
                data={itemOutwards}
                searchPlaceholder="Search item outwards..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Item Outward"
              />
            </TabsContent>

            <TabsContent value="metal-inward" className="mt-6">
              <DataTable
                columns={metalInwardColumns}
                data={metalInwards}
                searchPlaceholder="Search metal inwards..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Metal Inward"
              />
            </TabsContent>

            <TabsContent value="metal-outward" className="mt-6">
              <DataTable
                columns={metalOutwardColumns}
                data={metalOutwards}
                searchPlaceholder="Search metal outwards..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Metal Outward"
              />
            </TabsContent>

            <TabsContent value="sales" className="mt-6">
              <DataTable
                columns={salesColumns}
                data={sales}
                searchPlaceholder="Search sales..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Sale"
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-4xl">
          {renderForm()}
        </DialogContent>
      </Dialog>

      {/* Sales Dialog for pre-filled sales form */}
      <Dialog open={salesDialogOpen} onOpenChange={setSalesDialogOpen}>
        <DialogContent className="max-w-4xl">
          <SalesForm
            sale={prefilledSaleData}
            onClose={handleCloseSalesDialog}
            prefillData={prefilledSaleData}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
