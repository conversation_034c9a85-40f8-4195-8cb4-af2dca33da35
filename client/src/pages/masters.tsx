import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/ui/data-table";
import { CustomerForm } from "@/components/masters/customer-form";
import { ItemForm } from "@/components/masters/item-form";
import { WorkerForm } from "@/components/masters/worker-form";
import { MetalForm } from "@/components/masters/metal-form";
import { PurityForm } from "@/components/masters/purity-form";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { MoreHorizon<PERSON>, Edit, Trash2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Customer, Item, Worker, Metal, Purity } from "@shared/schema";

export default function Masters() {
  const [activeTab, setActiveTab] = useState("customers");
  const [editingItem, setEditingItem] = useState<any>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Queries
  const { data: customers = [], isLoading: customersLoading } = useQuery({
    queryKey: ["/api/customers"],
  });

  const { data: items = [], isLoading: itemsLoading } = useQuery({
    queryKey: ["/api/items"],
  });

  const { data: workers = [], isLoading: workersLoading } = useQuery({
    queryKey: ["/api/workers"],
  });

  const { data: metals = [], isLoading: metalsLoading } = useQuery({
    queryKey: ["/api/metals"],
  });

  const { data: purities = [], isLoading: puritiesLoading } = useQuery({
    queryKey: ["/api/purities"],
  });

  // Delete mutations
  const deleteCustomer = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/customers/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/customers"] });
      toast({ title: "Customer deleted successfully" });
    },
  });

  const deleteItem = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/items/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/items"] });
      toast({ title: "Item deleted successfully" });
    },
  });

  const deleteWorker = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/workers/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/workers"] });
      toast({ title: "Worker deleted successfully" });
    },
  });

  const deleteMetal = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/metals/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/metals"] });
      toast({ title: "Metal deleted successfully" });
    },
  });

  const deletePurity = useMutation({
    mutationFn: (id: string) => apiRequest("DELETE", `/api/purities/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/purities"] });
      toast({ title: "Purity deleted successfully" });
    },
  });

  // Columns definitions
  const customerColumns: ColumnDef<Customer>[] = [
    { accessorKey: "id", header: "Customer ID" },
    { accessorKey: "name", header: "Name" },
    { accessorKey: "balance", header: "Balance", cell: ({ row }) => `₹${row.getValue("balance")}` },
    { accessorKey: "mobile", header: "Mobile" },
    { accessorKey: "email", header: "Email" },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteCustomer.mutate(row.original.id)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const itemColumns: ColumnDef<Item>[] = [
    { accessorKey: "id", header: "Item ID" },
    { accessorKey: "number", header: "Item Number" },
    { accessorKey: "description", header: "Description" },
    { accessorKey: "remarks", header: "Remarks" },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteItem.mutate(row.original.id)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const workerColumns: ColumnDef<Worker>[] = [
    { accessorKey: "id", header: "Worker ID" },
    { accessorKey: "name", header: "Name" },
    { accessorKey: "balance", header: "Balance", cell: ({ row }) => `₹${row.getValue("balance")}` },
    { accessorKey: "remarks", header: "Remarks" },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteWorker.mutate(row.original.id)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const metalColumns: ColumnDef<Metal>[] = [
    { accessorKey: "id", header: "Metal ID" },
    { accessorKey: "description", header: "Description" },
    { accessorKey: "remarks", header: "Remarks" },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deleteMetal.mutate(row.original.id)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const purityColumns: ColumnDef<Purity>[] = [
    { accessorKey: "id", header: "Purity ID" },
    { accessorKey: "name", header: "Name" },
    { accessorKey: "metalType", header: "Metal Type" },
    { accessorKey: "description", header: "Description" },
    { accessorKey: "remarks", header: "Remarks" },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleEdit(row.original)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => deletePurity.mutate(row.original.id)}>
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const handleEdit = (item: any) => {
    setEditingItem(item);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingItem(null);
  };

  const renderForm = () => {
    switch (activeTab) {
      case "customers":
        return <CustomerForm customer={editingItem} onClose={handleCloseDialog} />;
      case "items":
        return <ItemForm item={editingItem} onClose={handleCloseDialog} />;
      case "workers":
        return <WorkerForm worker={editingItem} onClose={handleCloseDialog} />;
      case "metals":
        return <MetalForm metal={editingItem} onClose={handleCloseDialog} />;
      case "purity":
        return <PurityForm purity={editingItem} onClose={handleCloseDialog} />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Masters</h1>
        <p className="text-gray-600">Manage customers, items, workers, metals, and purity data</p>
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="customers">Customers</TabsTrigger>
              <TabsTrigger value="items">Items</TabsTrigger>
              <TabsTrigger value="workers">Workers</TabsTrigger>
              <TabsTrigger value="metals">Metals</TabsTrigger>
              <TabsTrigger value="purity">Purity</TabsTrigger>
            </TabsList>

            <TabsContent value="customers" className="mt-6">
              <DataTable
                columns={customerColumns}
                data={customers}
                searchPlaceholder="Search customers..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Customer"
              />
            </TabsContent>

            <TabsContent value="items" className="mt-6">
              <DataTable
                columns={itemColumns}
                data={items}
                searchPlaceholder="Search items..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Item"
              />
            </TabsContent>

            <TabsContent value="workers" className="mt-6">
              <DataTable
                columns={workerColumns}
                data={workers}
                searchPlaceholder="Search workers..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Worker"
              />
            </TabsContent>

            <TabsContent value="metals" className="mt-6">
              <DataTable
                columns={metalColumns}
                data={metals}
                searchPlaceholder="Search metals..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Metal"
              />
            </TabsContent>

            <TabsContent value="purity" className="mt-6">
              <DataTable
                columns={purityColumns}
                data={purities}
                searchPlaceholder="Search purity..."
                onAdd={() => setDialogOpen(true)}
                addButtonText="Add Purity"
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          {renderForm()}
        </DialogContent>
      </Dialog>
    </div>
  );
}
