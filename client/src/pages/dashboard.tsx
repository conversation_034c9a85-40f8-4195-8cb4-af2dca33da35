import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { QuickActionForms } from "@/components/quick-actions/quick-action-forms";
import { 
  Users, 
  Package, 
  HardHat, 
  Coins,
  ArrowDown,
  ArrowUp,
  TrendingUp,
  Plus,
  UserPlus,
  PackagePlus,
  UserCheck
} from "lucide-react";

export default function Dashboard() {
  const [quickActionType, setQuickActionType] = useState<"customer" | "item" | "worker" | null>(null);
  const [quickActionOpen, setQuickActionOpen] = useState(false);

  const { data: customers } = useQuery({
    queryKey: ["/api/customers"],
  });

  const { data: items } = useQuery({
    queryKey: ["/api/items"],
  });

  const { data: workers } = useQuery({
    queryKey: ["/api/workers"],
  });

  const { data: metals } = useQuery({
    queryKey: ["/api/metals"],
  });

  const { data: itemInwards } = useQuery({
    queryKey: ["/api/item-inwards"],
  });

  const { data: itemOutwards } = useQuery({
    queryKey: ["/api/item-outwards"],
  });

  const { data: sales } = useQuery({
    queryKey: ["/api/sales"],
  });

  const totalSales = sales?.reduce((sum: number, sale: any) => sum + parseFloat(sale.amount || "0"), 0) || 0;

  const recentInwards = itemInwards?.slice(-5) || [];
  const recentOutwards = itemOutwards?.slice(-5) || [];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Overview of your jewelry inventory system</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{items?.length || 0}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Customers</p>
                <p className="text-2xl font-bold text-gray-900">{customers?.length || 0}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Workers</p>
                <p className="text-2xl font-bold text-gray-900">{workers?.length || 0}</p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <HardHat className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-bold text-gray-900">₹{totalSales.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              className="h-16 flex flex-col items-center justify-center space-y-2"
              onClick={() => {
                setQuickActionType("customer");
                setQuickActionOpen(true);
              }}
            >
              <UserPlus className="h-6 w-6" />
              <span>Add Customer</span>
            </Button>

            <Button
              className="h-16 flex flex-col items-center justify-center space-y-2"
              onClick={() => {
                setQuickActionType("item");
                setQuickActionOpen(true);
              }}
            >
              <PackagePlus className="h-6 w-6" />
              <span>Add Item</span>
            </Button>

            <Button
              className="h-16 flex flex-col items-center justify-center space-y-2"
              onClick={() => {
                setQuickActionType("worker");
                setQuickActionOpen(true);
              }}
            >
              <UserCheck className="h-6 w-6" />
              <span>Add Worker</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ArrowDown className="mr-2 h-5 w-5 text-green-600" />
              Recent Item Inward
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentInwards.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No recent inward items</p>
              ) : (
                recentInwards.map((item: any) => {
                  const customer = customers?.find((c: any) => c.id === item.customerId);
                  const itemData = items?.find((i: any) => i.id === item.itemId);
                  
                  return (
                    <div key={item.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{itemData?.description || 'Unknown Item'}</p>
                        <p className="text-sm text-gray-600">Customer: {customer?.name || 'Unknown'}</p>
                        <p className="text-xs text-gray-500">{item.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{item.netWeight}g</p>
                        <p className="text-sm text-green-600">Inward</p>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ArrowUp className="mr-2 h-5 w-5 text-red-600" />
              Recent Item Outward
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOutwards.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No recent outward items</p>
              ) : (
                recentOutwards.map((item: any) => {
                  const customer = customers?.find((c: any) => c.id === item.customerId);
                  const itemData = items?.find((i: any) => i.id === item.itemId);
                  
                  return (
                    <div key={item.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{itemData?.description || 'Unknown Item'}</p>
                        <p className="text-sm text-gray-600">Customer: {customer?.name || 'Unknown'}</p>
                        <p className="text-xs text-gray-500">{item.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{item.netWeight}g</p>
                        <p className="text-sm text-red-600">Outward</p>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Action Dialog */}
      {quickActionOpen && (
        <QuickActionForms
          type={quickActionType}
          open={quickActionOpen}
          onClose={() => {
            setQuickActionOpen(false);
            setQuickActionType(null);
          }}
        />
      )}
    </div>
  );
}
