import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { AuthUser, staticAuthService } from '@/lib/static-auth';

interface AuthContextType {
  user: AuthUser | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const StaticAuthContext = createContext<AuthContextType | undefined>(undefined);

export function StaticAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Initialize auth state from localStorage
    const currentUser = staticAuthService.getCurrentUser();
    setUser(currentUser);
  }, []);

  const login = async (username: string, password: string) => {
    setIsLoading(true);
    try {
      const user = await staticAuthService.login(username, password);
      setUser(user);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    staticAuthService.logout();
    setUser(null);
  };

  return (
    <StaticAuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </StaticAuthContext.Provider>
  );
}

export function useStaticAuth() {
  const context = useContext(StaticAuthContext);
  if (context === undefined) {
    throw new Error('useStaticAuth must be used within a StaticAuthProvider');
  }
  return context;
}
