import { Switch, Route } from "wouter";
import { staticQueryClient } from "./lib/static-query-client";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { StaticAuthProvider, useStaticAuth } from "@/hooks/use-static-auth";
import { Sidebar } from "@/components/layout/sidebar";
import Login from "@/pages/login.static";
import Dashboard from "@/pages/dashboard";
import Masters from "@/pages/masters";
import Transactions from "@/pages/transactions";
import History from "@/pages/history";
import NotFound from "@/pages/not-found";

function AppContent() {
  const { user } = useStaticAuth();

  if (!user) {
    return <Login />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <main className="flex-1 overflow-auto">
        <div className="p-6">
          <Switch>
            <Route path="/" component={Dashboard} />
            <Route path="/masters" component={Masters} />
            <Route path="/transactions" component={Transactions} />
            <Route path="/history" component={History} />
            <Route component={NotFound} />
          </Switch>
        </div>
      </main>
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={staticQueryClient}>
      <StaticAuthProvider>
        <TooltipProvider>
          <Toaster />
          <AppContent />
        </TooltipProvider>
      </StaticAuthProvider>
    </QueryClientProvider>
  );
}

export default App;
