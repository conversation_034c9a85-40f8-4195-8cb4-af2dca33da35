import { staticAPI } from './static-api';

export interface AuthUser {
  username: string;
}

class StaticAuthService {
  private user: AuthUser | null = null;

  async login(username: string, password: string): Promise<AuthUser> {
    const result = await staticAPI.login(username, password);
    
    if (!result.success) {
      throw new Error(result.message || 'Invalid credentials');
    }

    this.user = result.user!;
    
    // Store in localStorage for persistence
    localStorage.setItem('auth_user', JSON.stringify(this.user));
    
    return this.user;
  }

  logout() {
    this.user = null;
    localStorage.removeItem('auth_user');
  }

  getCurrentUser(): AuthUser | null {
    if (!this.user) {
      // Try to restore from localStorage
      const stored = localStorage.getItem('auth_user');
      if (stored) {
        try {
          this.user = JSON.parse(stored);
        } catch {
          localStorage.removeItem('auth_user');
        }
      }
    }
    return this.user;
  }

  isAuthenticated(): boolean {
    return this.getCurrentUser() !== null;
  }

  // Initialize auth state from localStorage
  initialize() {
    this.getCurrentUser();
  }
}

export const staticAuthService = new StaticAuthService();

// Initialize on module load
if (typeof window !== 'undefined') {
  staticAuthService.initialize();
}
