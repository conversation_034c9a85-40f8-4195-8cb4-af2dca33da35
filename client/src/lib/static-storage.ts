// Static storage implementation for client-only deployment
import type { 
  <PERSON><PERSON>, 
  Item, 
  Worker, 
  Metal, 
  Purity, 
  ItemInward, 
  ItemOutward, 
  MetalInward, 
  MetalOutward, 
  Sales 
} from "@shared/types";

class StaticStorage {
  private generateId(prefix: string): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `${prefix}${timestamp}${random}`;
  }

  // Generic storage methods
  private getItems<T>(key: string): T[] {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  private setItems<T>(key: string, items: T[]): void {
    localStorage.setItem(key, JSON.stringify(items));
  }

  private addItem<T extends { id: string }>(key: string, item: T): T {
    const items = this.getItems<T>(key);
    const newItem = { ...item, id: item.id || this.generateId(key.toUpperCase()) };
    items.push(newItem);
    this.setItems(key, items);
    return newItem;
  }

  private updateItem<T extends { id: string }>(key: string, id: string, updates: Partial<T>): T | null {
    const items = this.getItems<T>(key);
    const index = items.findIndex(item => item.id === id);
    if (index === -1) return null;
    
    items[index] = { ...items[index], ...updates };
    this.setItems(key, items);
    return items[index];
  }

  private deleteItem(key: string, id: string): boolean {
    const items = this.getItems(key);
    const filteredItems = items.filter((item: any) => item.id !== id);
    if (filteredItems.length === items.length) return false;
    
    this.setItems(key, filteredItems);
    return true;
  }

  // Customer methods
  getCustomers(): Customer[] {
    return this.getItems<Customer>('customers');
  }

  addCustomer(customer: Omit<Customer, 'id'>): Customer {
    return this.addItem('customers', { ...customer, id: this.generateId('CUSTOMER') });
  }

  updateCustomer(id: string, updates: Partial<Customer>): Customer | null {
    return this.updateItem('customers', id, updates);
  }

  deleteCustomer(id: string): boolean {
    return this.deleteItem('customers', id);
  }

  // Item methods
  getItemsList(): Item[] {
    return this.getItems<Item>('items');
  }

  addItemToStorage(item: Omit<Item, 'id'>): Item {
    return this.addItem('items', { ...item, id: this.generateId('ITEM') });
  }

  updateItemInStorage(id: string, updates: Partial<Item>): Item | null {
    return this.updateItem('items', id, updates);
  }

  deleteItemFromStorage(id: string): boolean {
    return this.deleteItem('items', id);
  }

  // Worker methods
  getWorkers(): Worker[] {
    return this.getItems<Worker>('workers');
  }

  addWorker(worker: Omit<Worker, 'id'>): Worker {
    return this.addItem('workers', { ...worker, id: this.generateId('WORKER') });
  }

  updateWorker(id: string, updates: Partial<Worker>): Worker | null {
    return this.updateItem('workers', id, updates);
  }

  deleteWorker(id: string): boolean {
    return this.deleteItem('workers', id);
  }

  // Metal methods
  getMetals(): Metal[] {
    return this.getItems<Metal>('metals');
  }

  addMetal(metal: Omit<Metal, 'id'>): Metal {
    return this.addItem('metals', { ...metal, id: this.generateId('METAL') });
  }

  updateMetal(id: string, updates: Partial<Metal>): Metal | null {
    return this.updateItem('metals', id, updates);
  }

  deleteMetal(id: string): boolean {
    return this.deleteItem('metals', id);
  }

  // Purity methods
  getPurities(): Purity[] {
    return this.getItems<Purity>('purities');
  }

  addPurity(purity: Omit<Purity, 'id'>): Purity {
    return this.addItem('purities', { ...purity, id: this.generateId('PURITY') });
  }

  updatePurity(id: string, updates: Partial<Purity>): Purity | null {
    return this.updateItem('purities', id, updates);
  }

  deletePurity(id: string): boolean {
    return this.deleteItem('purities', id);
  }

  // Transaction methods
  getItemInwards(): ItemInward[] {
    return this.getItems<ItemInward>('itemInwards');
  }

  addItemInward(inward: Omit<ItemInward, 'id'>): ItemInward {
    return this.addItem('itemInwards', { ...inward, id: this.generateId('INWARD') });
  }

  updateItemInward(id: string, updates: Partial<ItemInward>): ItemInward | null {
    return this.updateItem('itemInwards', id, updates);
  }

  deleteItemInward(id: string): boolean {
    return this.deleteItem('itemInwards', id);
  }

  getSales(): Sales[] {
    return this.getItems<Sales>('sales');
  }

  addSales(sale: Omit<Sales, 'id'>): Sales {
    return this.addItem('sales', { ...sale, id: this.generateId('SALE') });
  }

  updateSales(id: string, updates: Partial<Sales>): Sales | null {
    return this.updateItem('sales', id, updates);
  }

  deleteSales(id: string): boolean {
    return this.deleteItem('sales', id);
  }

  // Initialize with sample data if empty
  initializeSampleData(): void {
    if (this.getCustomers().length === 0) {
      this.addCustomer({
        name: "John Doe",
        balance: 5000,
        mobile: "9876543210",
        email: "<EMAIL>",
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });

      this.addCustomer({
        name: "Jane Smith",
        balance: 3000,
        mobile: "9876543211",
        email: "<EMAIL>",
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });
    }

    if (this.getItemsList().length === 0) {
      this.addItemToStorage({
        number: "ITM001",
        description: "Gold Ring",
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });

      this.addItemToStorage({
        number: "ITM002",
        description: "Silver Necklace",
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });
    }

    if (this.getWorkers().length === 0) {
      this.addWorker({
        name: "Ravi Kumar",
        balance: 1500,
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });
    }

    if (this.getMetals().length === 0) {
      this.addMetal({
        name: "Gold",
        description: "24K Gold",
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });

      this.addMetal({
        name: "Silver",
        description: "925 Silver",
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });
    }

    if (this.getPurities().length === 0) {
      this.addPurity({
        name: "24K",
        percentage: 99.99,
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });

      this.addPurity({
        name: "22K",
        percentage: 91.67,
        status: "Active",
        createdAt: new Date(),
        createdBy: "<EMAIL>"
      });
    }
  }

  // Export all data
  exportData(): string {
    const data = {
      customers: this.getCustomers(),
      items: this.getItems(),
      workers: this.getWorkers(),
      metals: this.getMetals(),
      purities: this.getPurities(),
      itemInwards: this.getItemInwards(),
      sales: this.getSales(),
      exportDate: new Date().toISOString()
    };
    return JSON.stringify(data, null, 2);
  }

  // Import data
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      if (data.customers) this.setItems('customers', data.customers);
      if (data.items) this.setItems('items', data.items);
      if (data.workers) this.setItems('workers', data.workers);
      if (data.metals) this.setItems('metals', data.metals);
      if (data.purities) this.setItems('purities', data.purities);
      if (data.itemInwards) this.setItems('itemInwards', data.itemInwards);
      if (data.sales) this.setItems('sales', data.sales);
      return true;
    } catch {
      return false;
    }
  }
}

export const staticStorage = new StaticStorage();
