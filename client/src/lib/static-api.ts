// Static API implementation for client-only deployment
import { staticStorage } from './static-storage';

// Mock API responses to match server API structure
class StaticAPI {
  // Authentication
  async login(username: string, password: string): Promise<{ success: boolean; user?: { username: string }; message?: string }> {
    if (username === "<EMAIL>" && password === "Admin@1234") {
      return { success: true, user: { username } };
    }
    return { success: false, message: "Invalid credentials" };
  }

  // Customers
  async getCustomers() {
    return staticStorage.getCustomers();
  }

  async getCustomer(id: string) {
    const customers = staticStorage.getCustomers();
    return customers.find(c => c.id === id) || null;
  }

  async createCustomer(data: any) {
    return staticStorage.addCustomer(data);
  }

  async updateCustomer(id: string, data: any) {
    return staticStorage.updateCustomer(id, data);
  }

  async deleteCustomer(id: string) {
    return { success: staticStorage.deleteCustomer(id) };
  }

  // Items
  async getItems() {
    return staticStorage.getItemsList();
  }

  async getItem(id: string) {
    const items = staticStorage.getItemsList();
    return items.find(i => i.id === id) || null;
  }

  async createItem(data: any) {
    return staticStorage.addItemToStorage(data);
  }

  async updateItem(id: string, data: any) {
    return staticStorage.updateItemInStorage(id, data);
  }

  async deleteItem(id: string) {
    return { success: staticStorage.deleteItemFromStorage(id) };
  }

  // Workers
  async getWorkers() {
    return staticStorage.getWorkers();
  }

  async getWorker(id: string) {
    const workers = staticStorage.getWorkers();
    return workers.find(w => w.id === id) || null;
  }

  async createWorker(data: any) {
    return staticStorage.addWorker(data);
  }

  async updateWorker(id: string, data: any) {
    return staticStorage.updateWorker(id, data);
  }

  async deleteWorker(id: string) {
    return { success: staticStorage.deleteWorker(id) };
  }

  // Metals
  async getMetals() {
    return staticStorage.getMetals();
  }

  async getMetal(id: string) {
    const metals = staticStorage.getMetals();
    return metals.find(m => m.id === id) || null;
  }

  async createMetal(data: any) {
    return staticStorage.addMetal(data);
  }

  async updateMetal(id: string, data: any) {
    return staticStorage.updateMetal(id, data);
  }

  async deleteMetal(id: string) {
    return { success: staticStorage.deleteMetal(id) };
  }

  // Purities
  async getPurities() {
    return staticStorage.getPurities();
  }

  async getPurity(id: string) {
    const purities = staticStorage.getPurities();
    return purities.find(p => p.id === id) || null;
  }

  async createPurity(data: any) {
    return staticStorage.addPurity(data);
  }

  async updatePurity(id: string, data: any) {
    return staticStorage.updatePurity(id, data);
  }

  async deletePurity(id: string) {
    return { success: staticStorage.deletePurity(id) };
  }

  // Item Inwards
  async getItemInwards() {
    return staticStorage.getItemInwards();
  }

  async getItemInward(id: string) {
    const inwards = staticStorage.getItemInwards();
    return inwards.find(i => i.id === id) || null;
  }

  async createItemInward(data: any) {
    return staticStorage.addItemInward(data);
  }

  async updateItemInward(id: string, data: any) {
    return staticStorage.updateItemInward(id, data);
  }

  async deleteItemInward(id: string) {
    return { success: staticStorage.deleteItemInward(id) };
  }

  // Sales
  async getSales() {
    return staticStorage.getSales();
  }

  async getSale(id: string) {
    const sales = staticStorage.getSales();
    return sales.find(s => s.id === id) || null;
  }

  async createSale(data: any) {
    return staticStorage.addSales(data);
  }

  async updateSale(id: string, data: any) {
    return staticStorage.updateSales(id, data);
  }

  async deleteSale(id: string) {
    return { success: staticStorage.deleteSales(id) };
  }

  // Placeholder methods for other transactions
  async getItemOutwards() {
    return [];
  }

  async getMetalInwards() {
    return [];
  }

  async getMetalOutwards() {
    return [];
  }

  // Initialize sample data
  initializeSampleData() {
    staticStorage.initializeSampleData();
  }

  // Export/Import
  exportData() {
    return staticStorage.exportData();
  }

  importData(jsonData: string) {
    return staticStorage.importData(jsonData);
  }
}

export const staticAPI = new StaticAPI();

// Initialize sample data on first load
if (typeof window !== 'undefined') {
  staticAPI.initializeSampleData();
}
