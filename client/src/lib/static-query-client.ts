import { QueryClient, QueryFunction } from "@tanstack/react-query";
import { staticAPI } from "./static-api";

// Static query function that uses local storage instead of API calls
export const getStaticQueryFn: <T>() => QueryFunction<T> = () => async ({ queryKey }) => {
  const endpoint = queryKey[0] as string;
  
  // Map API endpoints to static API methods
  switch (endpoint) {
    case "/api/customers":
      return await staticAPI.getCustomers();
    
    case "/api/items":
      return await staticAPI.getItems();
    
    case "/api/workers":
      return await staticAPI.getWorkers();
    
    case "/api/metals":
      return await staticAPI.getMetals();
    
    case "/api/purities":
      return await staticAPI.getPurities();
    
    case "/api/item-inwards":
      return await staticAPI.getItemInwards();
    
    case "/api/item-outwards":
      return await staticAPI.getItemOutwards();
    
    case "/api/metal-inwards":
      return await staticAPI.getMetalInwards();
    
    case "/api/metal-outwards":
      return await staticAPI.getMetalOutwards();
    
    case "/api/sales":
      return await staticAPI.getSales();
    
    default:
      // Handle individual resource requests
      if (endpoint.startsWith("/api/customers/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getCustomer(id!);
      }
      
      if (endpoint.startsWith("/api/items/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getItem(id!);
      }
      
      if (endpoint.startsWith("/api/workers/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getWorker(id!);
      }
      
      if (endpoint.startsWith("/api/metals/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getMetal(id!);
      }
      
      if (endpoint.startsWith("/api/purities/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getPurity(id!);
      }
      
      if (endpoint.startsWith("/api/item-inwards/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getItemInward(id!);
      }
      
      if (endpoint.startsWith("/api/sales/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.getSale(id!);
      }
      
      throw new Error(`Unknown endpoint: ${endpoint}`);
  }
};

// Static API request function for mutations
export async function staticApiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<any> {
  const endpoint = url;
  
  switch (method.toUpperCase()) {
    case "POST":
      if (endpoint === "/api/auth/login") {
        const { username, password } = data as any;
        return await staticAPI.login(username, password);
      }
      
      if (endpoint === "/api/customers") {
        return await staticAPI.createCustomer(data);
      }
      
      if (endpoint === "/api/items") {
        return await staticAPI.createItem(data);
      }
      
      if (endpoint === "/api/workers") {
        return await staticAPI.createWorker(data);
      }
      
      if (endpoint === "/api/metals") {
        return await staticAPI.createMetal(data);
      }
      
      if (endpoint === "/api/purities") {
        return await staticAPI.createPurity(data);
      }
      
      if (endpoint === "/api/item-inwards") {
        return await staticAPI.createItemInward(data);
      }
      
      if (endpoint === "/api/sales") {
        return await staticAPI.createSale(data);
      }
      break;
    
    case "PUT":
      if (endpoint.startsWith("/api/customers/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updateCustomer(id!, data);
      }
      
      if (endpoint.startsWith("/api/items/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updateItem(id!, data);
      }
      
      if (endpoint.startsWith("/api/workers/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updateWorker(id!, data);
      }
      
      if (endpoint.startsWith("/api/metals/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updateMetal(id!, data);
      }
      
      if (endpoint.startsWith("/api/purities/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updatePurity(id!, data);
      }
      
      if (endpoint.startsWith("/api/item-inwards/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updateItemInward(id!, data);
      }
      
      if (endpoint.startsWith("/api/sales/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.updateSale(id!, data);
      }
      break;
    
    case "DELETE":
      if (endpoint.startsWith("/api/customers/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deleteCustomer(id!);
      }
      
      if (endpoint.startsWith("/api/items/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deleteItem(id!);
      }
      
      if (endpoint.startsWith("/api/workers/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deleteWorker(id!);
      }
      
      if (endpoint.startsWith("/api/metals/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deleteMetal(id!);
      }
      
      if (endpoint.startsWith("/api/purities/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deletePurity(id!);
      }
      
      if (endpoint.startsWith("/api/item-inwards/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deleteItemInward(id!);
      }
      
      if (endpoint.startsWith("/api/sales/")) {
        const id = endpoint.split("/").pop();
        return await staticAPI.deleteSale(id!);
      }
      break;
  }
  
  throw new Error(`Unsupported method ${method} for endpoint ${endpoint}`);
}

export const staticQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getStaticQueryFn(),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

// Mock Response object for compatibility
export class MockResponse {
  constructor(private data: any, private status: number = 200) {}

  async json() {
    return this.data;
  }

  get ok() {
    return this.status >= 200 && this.status < 300;
  }

  get statusText() {
    return this.status === 200 ? "OK" : "Error";
  }
}

// Export staticApiRequest as apiRequest for compatibility
export { staticApiRequest as apiRequest };
